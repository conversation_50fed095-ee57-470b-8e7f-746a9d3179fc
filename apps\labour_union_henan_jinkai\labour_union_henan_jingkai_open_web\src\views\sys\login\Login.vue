<template>
  <div
    :class="prefixCls"
    class="relative w-full h-full min-h-screen"
  >
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div class="absolute inset-0 opacity-40 bg-pattern"></div>
    </div>

    <!-- Logo -->
    <div class="absolute top-8 left-8 z-10">
      <AppLogo
        :showTitle="true"
        title-class="!text-[28px] !text-gray-700 font-semibold"
        logo-class="w-[50px] h-[50px]"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="relative flex items-center justify-center min-h-screen p-4">
      <!-- 登录卡片 -->
      <div class="w-full max-w-md">
        <!-- 装饰性动画图标 -->
        <div class="flex justify-center mb-8">
          <div
            id="login-icon"
            class="w-[200px] h-[200px] bounce-gentle"
          ></div>
        </div>

        <!-- 登录表单卡片 -->
        <div
          :class="`${prefixCls}-form`"
          class="relative bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8 enter-x"
        >
          <LoginForm />
        </div>

        <!-- 技术支持 -->
        <div class="mt-6 flex justify-center">
          <TechnicalSupport />
        </div>
      </div>
    </div>

    <!-- 装饰性元素 -->
    <div
      class="absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"
    ></div>
    <div
      class="absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"
    ></div>
  </div>
</template>
<script lang="ts" setup>
import { AppLogo } from '@/components/Application';
import { useDesign } from '@monorepo-yysz/hooks';
import LoginForm from './LoginForm.vue';
import TechnicalSupport from '@/components/TechnicalSupport/index.vue';
import lottie from 'lottie-web';
import { nextTick, onMounted } from 'vue';

defineProps({
  sessionTimeout: {
    type: Boolean,
  },
});

const { prefixCls } = useDesign('login');

onMounted(() => {
  nextTick(() => {
    lottie.loadAnimation({
      container: document.getElementById(`login-icon`) as any, // 包含动画的dom元素
      renderer: 'svg', // 渲染出来的是什么格式
      loop: true, // 循环播放
      autoplay: true, // 自动播放
      path: '/resource/login-icon.json', // 动画json的路径
    });
  });
});
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-login';
@logo-prefix-cls: ~'@{namespace}-app-logo';
@countdown-prefix-cls: ~'@{namespace}-countdown-input';
@dark-bg: #293146;

html[data-theme='dark'] {
  .@{prefix-cls} {
    background-color: @dark-bg;

    .ant-input,
    .ant-input-password {
      background-color: #232a3b;
    }

    .ant-btn:not(.ant-btn-link, .ant-btn-primary) {
      border: 1px solid #4a5569;
    }

    &-form {
      background: transparent !important;
    }

    .app-iconify {
      color: #fff;
    }

    .ant-divider-inner-text {
      color: @text-color-secondary;
    }
  }
}

.@{prefix-cls} {
  min-height: 100vh;
  overflow: hidden;

  .@{logo-prefix-cls} {
    &__title {
      color: #374151;
      font-size: 18px;
      font-weight: 600;
    }
  }

  &-form {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
    }
  }

  // 背景图案
  .bg-pattern {
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(156, 146, 172, 0.15) 1px,
      transparent 0
    );
    background-size: 20px 20px;
  }

  input:not([type='checkbox']) {
    min-width: 280px;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* stylelint-disable-next-line media-query-no-invalid */
    @media (max-width: @screen-xl) {
      min-width: 320px;
    }
    /* stylelint-disable-next-line media-query-no-invalid */
    @media (max-width: @screen-lg) {
      min-width: 260px;
    }
    /* stylelint-disable-next-line media-query-no-invalid */
    @media (max-width: @screen-md) {
      min-width: 240px;
    }
    /* stylelint-disable-next-line media-query-no-invalid */
    @media (max-width: @screen-sm) {
      min-width: 160px;
    }
  }

  .ant-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    height: 48px;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
    }
  }

  .@{countdown-prefix-cls} input {
    min-width: unset;
  }
}
</style>

<style>
.bounce-gentle {
  animation: bounce-gentle 3s ease-in-out infinite;
}

@keyframes bounce-gentle {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.05);
  }
}

/* 进入动画 */
.enter-x {
  animation: slideInFromLeft 0.6s ease-out;
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 装饰性脉冲动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

.animate-pulse {
  animation: pulse 4s ease-in-out infinite;
}

.delay-1000 {
  animation-delay: 1s;
}

/* 表单项动画 */
.ant-form-item {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.ant-form-item:nth-child(1) {
  animation-delay: 0.1s;
}
.ant-form-item:nth-child(2) {
  animation-delay: 0.2s;
}
.ant-form-item:nth-child(3) {
  animation-delay: 0.3s;
}
.ant-form-item:nth-child(4) {
  animation-delay: 0.4s;
}
.ant-form-item:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
