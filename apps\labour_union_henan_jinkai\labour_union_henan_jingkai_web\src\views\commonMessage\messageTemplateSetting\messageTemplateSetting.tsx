import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '模板标题',
      dataIndex: 'templateTitle',
      width: 100,
    },
    {
      title: '模板类型',
      dataIndex: 'templateType',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`messageType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '是否启用',
      dataIndex: 'enableType',
      width: 100,
      customRender: ({ text }) => {
        const dic = dictionary.getDictionaryMap.get(`logicallyDelete_${text}`);
        const name = dic?.dictName;
        const color = dic?.remark;
        return (
          <span
            title={name}
            style={{ color }}
          >
            {name}
          </span>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '编辑时间',
      dataIndex: 'updateTime',
      width: 150,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'templateType',
      label: '模板类型',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('messageType'),
          placeholder: '请选择模板类型',
        };
      },
    },
    {
      field: 'enableType',
      label: '是否启用',
      component: 'Select',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('logicallyDelete'),
        };
      },
    },
    {
      field: 'templateTitle',
      label: '模板标题',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请输入模板标题',
      },
    },
  ];
};

export const modalForm = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'templateType',
      label: '模板类型',
      required: true,
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('messageType'),
          autocomplete: 'off',
        };
      },
    },
    {
      field: 'enableType',
      label: '是否启用',
      required: true,
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: 'y',
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('logicallyDelete') as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'templateTitle',
      label: '模板标题',
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
      },
    },
    {
      field: 'sysTemplateContent',
      label: '站内信模板',
      required: true,
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      // render: ({ model, field, disabled }) => {
      //   return h(Tinymce, {
      //     value: model[field],
      //     onChange: (value: string) => {
      //       model[field] = value;
      //     },
      //     showImageUpload: false,
      //     options: {
      //       readonly: disabled,
      //     },
      //     operateType: 30,
      //   });
      // },
    },
    {
      field: 'mesTemplateContent',
      label: '短信模板',
      required: true,
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
    },
  ];
};
