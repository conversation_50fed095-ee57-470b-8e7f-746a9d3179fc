<template>
  <div
    :class="prefixCls"
    class="relative w-full h-full"
  >
    <span class="-enter-x relative top-[64px] left-[72px]">
      <AppLogo
        :showTitle="true"
        title-class="!text-[33px] !text-[#000000]"
        logo-class="w-[60px] h-[60px]"
      />
    </span>

    <div class="relative h-full w-full">
      <div class="flex h-full bg-[#f1f3f6]">
        <div class="flex justify-center items-center xl:w-2/3 left-bg">
          <div
            id="login-icon"
            class="w-[400px] h-[400px] bounce"
          ></div>
        </div>
        <div
          class="flex w-1/3 h-full py-5 justify-center items-center bg-white xl:h-auto xl:py-0 xl:my-0"
        >
          <div
            :class="`${prefixCls}-form`"
            class="relative px-5 py-8 rounded-md shadow-md xl:bg-transparent sm:px-8 xl:p-4 xl:shadow-none enter-x"
          >
            <LoginForm />
          </div>

          <TechnicalSupport />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { AppLogo } from '@/components/Application';
import { useDesign } from '@monorepo-yysz/hooks';
import LoginForm from './LoginForm.vue';
import TechnicalSupport from '@/components/TechnicalSupport/index.vue';
import lottie from 'lottie-web';
import { nextTick, onMounted } from 'vue';

defineProps({
  sessionTimeout: {
    type: Boolean,
  },
});

const { prefixCls } = useDesign('login');

onMounted(() => {
  nextTick(() => {
    lottie.loadAnimation({
      container: document.getElementById(`login-icon`) as any, // 包含动画的dom元素
      renderer: 'svg', // 渲染出来的是什么格式
      loop: true, // 循环播放
      autoplay: true, // 自动播放
      path: '/resource/login-icon.json', // 动画json的路径
    });
  });
});
</script>
<style lang="less">
@prefix-cls: ~'@{namespace}-login';
@logo-prefix-cls: ~'@{namespace}-app-logo';
@countdown-prefix-cls: ~'@{namespace}-countdown-input';
@dark-bg: #293146;

html[data-theme='dark'] {
  .@{prefix-cls} {
    background-color: @dark-bg;

    .ant-input,
    .ant-input-password {
      background-color: #232a3b;
    }

    .ant-btn:not(.ant-btn-link, .ant-btn-primary) {
      border: 1px solid #4a5569;
    }

    &-form {
      background: transparent !important;
    }

    .app-iconify {
      color: #fff;
    }

    .ant-divider-inner-text {
      color: @text-color-secondary;
    }
  }
}

.@{prefix-cls} {
  min-height: 100%;
  overflow: hidden;

  .@{logo-prefix-cls} {
    position: absolute;
    top: 12px;
    height: 30px;

    &__title {
      color: #fff;
      font-size: 16px;
    }

    // img {
    //   width: 32px;
    // }
  }

  .left-bg {
    background: linear-gradient(
      154deg,
      rgba(7, 7, 9, 0.08235) 30%,
      hsl(212 100% 45% / 30%) 48%,
      rgba(7, 7, 9, 0.08235) 64%
    );
  }
  input:not([type='checkbox']) {
    min-width: 280px;
    /* stylelint-disable-next-line media-query-no-invalid */
    @media (max-width: @screen-xl) {
      min-width: 320px;
    }
    /* stylelint-disable-next-line media-query-no-invalid */
    @media (max-width: @screen-lg) {
      min-width: 260px;
    }
    /* stylelint-disable-next-line media-query-no-invalid */
    @media (max-width: @screen-md) {
      min-width: 240px;
    }
    /* stylelint-disable-next-line media-query-no-invalid */
    @media (max-width: @screen-sm) {
      min-width: 160px;
    }
  }

  .@{countdown-prefix-cls} input {
    min-width: unset;
  }
}
</style>

<style>
.bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}
</style>
