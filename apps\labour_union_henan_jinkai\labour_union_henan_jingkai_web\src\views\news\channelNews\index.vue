<template>
  <div>
    <basic-table @register="registerTable">
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/channelNews/add"
        >
          新增新闻
        </a-button>
      </template>
      <template #form-categoryId="{ model, field }">
        <TreeSelect
          v-model:value="model[field]"
          placeholder="请选择新闻栏目"
          :showSearch="true"
          :filterOption="filterOption"
          :fieldNames="{ label: 'categoryName', value: 'categoryId', children: 'children' }"
          :treeData="categoryOptions"
        />
      </template>
      <template #form-queryCompanyId="{ model, field }">
        <Select
          v-model:value="model[field]"
          placeholder="请选择所属工会"
          :showSearch="true"
          :filterOption="queryCompanyIdFilterOption"
          :fieldNames="{ label: 'companyName', value: 'companyId' }"
          :options="queryCompanyOptions"
        />
      </template>
      <template #form-tenantChildFlag="{ model, field }">
        <!-- <Checkbox
          v-model:checked="model[field]"
          @change="changeNextLevelFlag"
        /> -->
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '预览',
                icon: 'ant-design:eye-filled',
                type: 'default',
                onClick: handleRecordView.bind(null, record),
                auth: '/channelNews/view',
                disabled: record.isUploadFiles,
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                onClick: handleEdit.bind(null, record),
                auth: '/channelNews/modify',
                ifShow:
                  userStore.getUserInfo.companyId === record.companyId &&
                  ((record.newsPublishStatus === '20' && record.newsAuditStatus === '10') ||
                    (record.newsPublishStatus === '-1' && record.newsAuditStatus === '40')),
              },
              {
                icon: 'ic:baseline-published-with-changes',
                label: '发布',
                type: 'primary',
                // disabled: !(record.newsAuditStatus === '30' && record.newsPublishStatus === '0'),
                onClick: handlePublish.bind(null, record, 'release'),
                auth: '/channelNews/publish',
                ifShow:
                  userStore.getUserInfo.companyId === record.companyId &&
                  record.newsAuditStatus === '30' &&
                  record.newsPublishStatus === '0',
              },
              {
                icon: 'bx:log-out-circle',
                label: '撤销',
                type: 'primary',
                // disabled: !(record.newsPublishStatus === '10' || record.newsPublishStatus === '30'),
                onClick: handlePublish.bind(null, record, 'revoke'),
                auth: '/channelNews/revoke',
                ifShow:
                  userStore.getUserInfo.companyId === record.companyId &&
                  (record.newsPublishStatus === '10' || record.newsPublishStatus === '30'),
              },
              {
                icon: 'fluent:delete-20-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                // disabled:
                //   !(record.newsPublishStatus === '20' && record.newsAuditStatus === '10') &&
                //   !(record.newsPublishStatus === '-1' && record.newsAuditStatus === '40'),
                onClick: handleDelete.bind(null, record),
                auth: '/channelNews/delete',
                ifShow:
                  userStore.getUserInfo.companyId === record.companyId &&
                  ((record.newsPublishStatus === '20' && record.newsAuditStatus === '10') ||
                    (record.newsPublishStatus === '-1' && record.newsAuditStatus === '40')),
              },
              // {
              //   icon: 'bxs:sun',
              //   label: record.newsTopOption ? '取消加热' : '开启加热',
              //   type: 'primary',
              //   onClick: handleHot.bind(null, record),
              //   auth: '/channelNews/hot',
              // },
              // {
              //   icon: 'ant-design:like-filled',
              //   label: '点赞',
              //   type: 'primary',
              //
              //   onClick: handleLike.bind(null, record),
              //   auth: '/channelNews/like',
              // },
              // {
              //   icon: 'icon-park-outline:collection-files',
              //   label: '收藏',
              //   type: 'primary',
              //
              //   onClick: handleShare.bind(null, record),
              //   auth: '/channelNews/share',
              // },
              {
                icon: 'carbon:align-vertical-top',
                label: '置顶',
                type: 'primary',
                onClick: handleTop.bind(null, record),
                auth: '/channelNews/top',
                ifShow:
                  userStore.getUserInfo.companyId === record.companyId &&
                  'publishAndTop' === record.categorySortOrder,
              },
              {
                icon: 'mdi:sort',
                label: '排序',
                type: 'primary',
                onClick: handleSort.bind(null, record),
                auth: '/channelNews/sort',
                disabled: !record.whetherSetSort,
                ifShow:
                  userStore.getUserInfo.companyId === record.companyId &&
                  'sortNumber' === record.categorySortOrder,
              },
              {
                label: '播报预览',
                icon: 'icon-park-outline:broadcast-radio',
                type: 'primary',
                onClick: handleBroadcast.bind(null, record),
                auth: '/channelNews/broadcast',
                ifShow: 'manualEntry' === record.aiBroadcastType,
              },
              {
                label: '生成详情',
                icon: 'material-symbols:manage-search',
                type: 'primary',
                onClick: handleBroadcastManage.bind(null, record),
                auth: '/channelNews/broadcastManage',
                ifShow: 'automatically' === record.aiBroadcastType,
              },
              {
                icon: 'material-symbols:summarize-outline-rounded',
                label: '统计',
                type: 'primary',
                onClick: handleStatisticalDetails.bind(null, record),
                auth: '/channelNews/statisticalDetails',
              },
              {
                icon: 'material-symbols:summarize-outline-rounded',
                label: '评论',
                type: 'primary',
                onClick: handleReviewDetails.bind(null, record),
                auth: '/channelNews/reviewsDetails',
              },
              {
                icon: 'line-md:link',
                label: '频道链接',
                type: 'primary',
                tooltip: record?.detailsUrl,
                ifShow: userStore.getUserInfo.companyId === record.companyId,
                auth: '/channelNews/detailsUrl',
              },
            ]"
          />
        </template>
      </template>
    </basic-table>
    <ChanelNewsModal
      @register="registerModal"
      :can-fullscreen="false"
      width="88%"
      @success="handleSuccess"
      @audit="handleSuccess"
      @view="handleView"
    />

    <ViewModal
      @register="registerView"
      :can-fullscreen="false"
      width="40%"
    />
    <!-- 播报预览 -->
    <BroadcastModel
      @register="broadcastView"
      :can-fullscreen="false"
      width="40%"
    />
    <!-- 播报管理 -->
    <BroadcastManageModel
      @register="broadcastManageView"
      :can-fullscreen="false"
      width="40%"
    />
    <TopModal
      @register="registerTop"
      :can-fullscreen="false"
      width="40%"
      @success="handleTopSuccess"
    />
    <SortModel
      @register="registerSort"
      :can-fullscreen="false"
      width="40%"
      @success="handleSortSuccess"
    />
    <ShareAndLike
      @register="registerShare"
      :can-fullscreen="false"
      width="40%"
      :ifcollect="ifcollect"
    />

    <!-- 统计指标明细 -->
    <StatisticalDetailsModel
      @register="registerStatisticalDetails"
      :can-fullscreen="false"
      width="60%"
    />

    <!-- 评论明细 -->
    <ReviewDetailsModel
      @register="registerReviewDetails"
      :can-fullscreen="false"
      width="60%"
    />
    <!--  发布按钮弹窗-->
    <BasicModal
      v-model:open="visible"
      :title="title"
      @ok="handleOk"
      :can-fullscreen="false"
      :bodyStyle="{ height: '40vh' }"
    >
      <Form
        :model="formState"
        :labelCol="{ span: 4 }"
        :wrapperCol="{ span: 14 }"
      >
        <FormItem
          label="发布时间"
          class="!mt-2"
        >
          <DatePicker
            v-model:value="formState.publishTime"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm"
            showTime
          />
        </FormItem>
      </Form>
    </BasicModal>
    <!--  列表点击审核状态,审核记录弹窗  -->
    <BasicModal
      v-model:open="auditVisible"
      @cancel="handleCancel"
      width="40%"
      :title="`${line?.newsTitle || ''}审核记录`"
      :can-fullscreen="false"
      @ok="handleCancel"
    >
      <div class="p-5 h-full overflow-y-auto">
        <Timeline>
          <TimelineItem
            v-for="item in auditLines"
            :color="item.newsAuditStatus === 'pass' ? 'green' : 'red'"
          >
            <div>审核时间：{{ item.createTime }}</div>
            <div>审核人：{{ item.createUser }}</div>
            <div>审核状态：{{ item.newsAuditStatus === 'pass' ? '通过' : '拒绝' }}</div>
            <div v-if="item.newsAuditStatus === 'refuse'"
              >拒绝原因：{{
                dictionary.getDictionaryMap.get(`newsRejectCategory_${item.newsRejectCategory}`)
                  ?.dictName || '无'
              }}
            </div>
            <div>审核意见：{{ item.newsAuditInstruction || '无' }}</div>
          </TimelineItem>
        </Timeline>
      </div>
      <template #footer> </template>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, TableAction, useTable } from '@/components/Table';
import { authColumn, columns, formSchemas, recordColumn } from './data';
import {
  getNewsList,
  newsAdd,
  NewsAuditRecord,
  newsDelete,
  newsGetOneNews,
  newsHeating,
  newsRelease,
  newsTop,
  newsUpdate,
  getNewsMainTable,
  setNewsSort,
} from '@/api/news';
import { BasicModal, useModal } from '@/components/Modal';
import ChanelNewsModal from './ChanelNewsModal.vue';
import ViewModal from './ViewModal.vue';
import { Form, DatePicker, Timeline, TimelineItem } from 'ant-design-vue';
import { createVNode, ref, unref, computed, watch, onMounted } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import TopModal from './TopModal.vue';
import SortModel from './SortModel.vue';
import ShareAndLike from './ShareAndLike.vue';
import StatisticalDetailsModel from './StatisticalDetailsModel.vue';
import { find } from 'lodash-es';
import { useInfos } from '@/store/modules/infos';
import { useDictionary } from '@/store/modules/dictionary';
import BroadcastModel from './BroadcastModel.vue';
import BroadcastManageModel from './BroadcastManageModel.vue';
import { useMessage } from '@monorepo-yysz/hooks';
import { useUserStore } from '@/store/modules/user';
import { TreeSelect, Checkbox, Select } from 'ant-design-vue';
import { getUnionTree } from '/@/api/category';
import { list as newsReleaseUnionlList } from '@/api/category/newsReleaseUnion';
import ReviewDetailsModel from './ReviewDetailsModel.vue';

const userStore = useUserStore();
const categoryOptions = ref<Recordable[]>([]);
const queryCompanyOptions = ref([]);

function changeNextLevelFlag(val) {
  const tenantChildFlag = val?.target?.checked;
  getUnionTree({ tenantChildFlag }).then(res => {
    categoryOptions.value = res;
  });
  newsReleaseUnionlList({ tenantChildFlag }).then(res => {
    const { data } = res;
    queryCompanyOptions.value = data;
  });
}

// 自动请求并暴露内部方法
onMounted(() => {
  changeNextLevelFlag({ target: { checked: false } });
});

const { createErrorModal, createSuccessModal, createMessage, createConfirm } = useMessage();

const ifcollect = ref(false);

const FormItem = Form.Item;

const infos = useInfos();

const auditVisible = ref(false);

const line = computed(() => {
  return infos.getRecord as Recordable;
});

const auditLines = ref<Recordable[]>([]);

//数据字典
const dictionary = useDictionary();

const formState = ref<Recordable>({
  publishTime: '',
});

const filterOption = (input: string, option: any) => {
  return option.categoryName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const queryCompanyIdFilterOption = (input: string, option: any) => {
  return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const title = computed(() => {
  return `发布${unref(formState).newsTitle}`;
});

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  authInfo: authColumn,
  columns: columns(),
  showIndexColumn: false,
  api: getNewsList,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  beforeFetch: params => {
    const { pushDateRange } = params;
    if (pushDateRange && pushDateRange.length === 2) {
      params.publishTimeStart = pushDateRange[0];
      params.publishTimeEnd = pushDateRange[1];
    }
    params.pushDateRange = undefined;
    params.platformType = 30;
    return params;
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    dataIndex: 'action',
    fixed: undefined,
    auth: recordColumn,
    width: 470,
    align: 'left',
    class: '!text-center',
    className: 'deal-action',
  },
});

const [registerModal, { openModal, closeModal }] = useModal();

const [registerView, { openModal: openView }] = useModal();
//播报预览
const [broadcastView, { openModal: openBroadcast }] = useModal();
//播报管理
const [broadcastManageView, { openModal: openBroadcastManage }] = useModal();

const [registerTop, { openModal: openTop, closeModal: closeTop }] = useModal();

const [registerSort, { openModal: openSort, closeModal: closeSort }] = useModal();

const [registerShare, { openModal: openShare }] = useModal();

const [registerStatisticalDetails, { openModal: openStatisticalDetails }] = useModal();

const [registerReviewDetails, { openModal: openReviewDetails }] = useModal();

const visible = ref<boolean>(false);

const showModal = () => {
  visible.value = true;
};

const handleOk = () => {
  const record = unref(formState);
  newsRelease({ ...record, releaseType: 'release' }).then(res => {
    if (res.code === 200) {
      createMessage.success(`发布成功`);
    } else {
      createMessage.error(`发布失败, ${res.message}`);
    }
    reload();
  });
  visible.value = false;
};

//新增
function handleClick() {
  openModal(true, { isUpdate: false });
}

//编辑
function handleEdit(record) {
  newsGetOneNews({ autoId: record.autoId }).then(res => {
    const { code, data, message: msg } = res;
    if (code === 200) {
      openModal(true, { isUpdate: true, record: { ...data, newsClicks: record?.newsClicks } });
    } else {
      createErrorModal({ content: `${msg}` });
    }
  });
}

function handleSuccess({ params, isUpdate }) {
  if (isUpdate) {
    newsUpdate(params).then(res => {
      const { code, message: msg } = res;
      if (code === 200) {
        createSuccessModal({ content: '操作成功' });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `操作失败，${msg}` });
      }
    });
  } else {
    newsAdd(params).then(res => {
      const { code, message: msg } = res;
      if (code === 200) {
        createSuccessModal({ content: '操作成功' });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `操作失败，${msg}` });
      }
    });
  }
}

//新增编辑预览
function handleView({ params }) {
  console.log(params);

  const { newsDetailsList, newsSource, newsClicks, keywords } = params as Recordable;

  const appDetail = find(newsDetailsList, v => v.platformType === '30');

  const zgDetail = find(newsDetailsList, v => v.platformType === '20');

  const pDetail = find(newsDetailsList, v => v.platformType === '10');

  openView(true, {
    record: {
      appContent: appDetail && appDetail.newsDetailsContent,
      appTitle: appDetail && appDetail.newsDetailsTitle,
      appUrl: appDetail && appDetail.externalLinkAddress,
      zgContent: zgDetail && zgDetail.newsDetailsContent,
      zgUrl: zgDetail && zgDetail.externalLinkAddress,
      pContent: pDetail && pDetail.newsDetailsContent,
      pUrl: pDetail && pDetail.externalLinkAddress,
      source: newsSource,
      reading: newsClicks,
      keywords: keywords,
    },
  });
}

//播报预览
function handleBroadcast(record) {
  openBroadcast(true, { record });
}
//播报管理
function handleBroadcastManage(record) {
  openBroadcastManage(true, { record });
}

//新闻统计明细页面
function handleStatisticalDetails(record) {
  openStatisticalDetails(true, { record });
  ifcollect.value = true;
}

function handleRecordView(record) {
  // newsGetOneNews({ autoId: record.autoId }).then(res => {
  //   const { code, data, message: msg } = res;
  //   if (code === 200) {
  //     const { newsDetailsList, newsSource, keywords } = data as Recordable;

  //     const appDetail = find(newsDetailsList, v => v.platformType === '30');

  //     const zgDetail = find(newsDetailsList, v => v.platformType === '20');

  //     const pDetail = find(newsDetailsList, v => v.platformType === '10');

  //     openView(true, {
  //       record: {
  //         appContent: appDetail && appDetail.newsDetailsContent,
  //         appTitle: appDetail && appDetail.newsDetailsTitle,
  //         appUrl: appDetail && appDetail.externalLinkAddress,
  //         zgContent: zgDetail && zgDetail.newsDetailsContent,
  //         zgUrl: zgDetail && zgDetail.externalLinkAddress,
  //         pContent: pDetail && pDetail.newsDetailsContent,
  //         pUrl: pDetail && pDetail.externalLinkAddress,
  //         source: newsSource,
  //         reading: record.newsClicks,
  //         keywords: keywords,
  //       },
  //     });
  //   } else {
  //     createErrorModal({ content: `${msg}` });
  //   }
  // });
  openView(true, {
    record: {
      newsId: record?.newsId,
    },
  });
}

//发布
function handlePublish(record, releaseType) {
  const name = releaseType === 'release' ? '发布' : '撤销';
  if (releaseType === 'release') {
    formState.value = { ...record };
    showModal();
  } else {
    createConfirm({
      title: '信息',
      icon: createVNode(ExclamationCircleOutlined),
      content: `确定${name}?`,
      okText: '确认',
      cancelText: '取消',
      async onOk() {
        try {
          return await new Promise<void>(resolve => {
            newsRelease({ autoId: record.autoId, releaseType }).then(res => {
              if (res.code === 200) {
                createMessage.success(`${name}成功`);
              } else {
                createMessage.error(`${name}失败`);
              }
              reload();
              resolve();
            });
          });
        } catch {
          return console.log('Oops errors!');
        }
      },
    });
  }
}

//删除
function handleDelete(record) {
  createConfirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定删除?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          newsDelete({ ...record, logicallyDelete: 'y' }).then(res => {
            if (res.code === 200) {
              createMessage.success('删除成功');
            } else {
              createMessage.error('删除失败');
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

//加热
function handleHot(record) {
  createConfirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定加热?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          newsHeating({ ...record, heatingState: !record.newsTopOption }).then(res => {
            if (res.code === 200) {
              createMessage.success('设置成功');
            } else {
              createMessage.error('设置失败');
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}

//设置置顶
function handleTop(record) {
  openTop(true, { record });
}
//设置排序
function handleSort(record) {
  openSort(true, { record });
}

//设置置顶回调
function handleTopSuccess({ values, autoId }) {
  newsTop({ ...values, autoId }).then(res => {
    const { code, message: msg } = res;
    if (code === 200) {
      createMessage.success('操作成功');
      closeTop();
      reload();
    } else {
      createMessage.error(msg);
    }
  });
}
//设置排序回调
function handleSortSuccess({ values, autoId }) {
  const { newsTitle, referToAutoId, newsSequentialOptions } = values;
  const newsSequentialOptionsName = 'before' === newsSequentialOptions ? '之前' : '之后';
  //根据新闻业务id查询新闻信息
  getNewsMainTable({ autoId: referToAutoId }).then(res => {
    const { code, data, message: mes } = res;
    if (code === 200) {
      createConfirm({
        title: '操作提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: `确定将[${newsTitle}]设置在[${data?.newsTitle}]${newsSequentialOptionsName}嘛?确认后系统将自动修改排序号!`,
        okText: '确认',
        cancelText: '取消',
        async onOk() {
          try {
            return await new Promise<void>(resolve => {
              setNewsSort({ autoId, newsSequentialOptions, referToAutoId }).then(res => {
                resolve();
                if (res.code === 200) {
                  createMessage.success('设置成功!');
                  reload();
                  closeSort();
                } else {
                  createMessage.error(`设置失败!${res.message}`);
                }
              });
            });
          } catch {
            return console.log('Oops errors!');
          }
        },
      });
    } else {
      createMessage.error(`设置失败!${mes}`);
    }
  });
}

//点赞
function handleLike(record) {
  openShare(true, { record });
  ifcollect.value = false;
}

//收藏
function handleShare(record) {
  openShare(true, { record });
  ifcollect.value = true;
}

function handleCancel() {
  infos.setVisible(false);
  infos.setRecord(null);
}

//新闻评论明细页面
function handleReviewDetails(record) {
  openReviewDetails(true, { record });
}

watch(
  () => infos.getVisible,
  () => {
    auditVisible.value = infos.getVisible;
  }
);

watch(line, async () => {
  if (unref(line)?.newsId) {
    auditLines.value = await NewsAuditRecord({
      newsId: unref(line)?.newsId,
    });
  }
});
</script>
