<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSuccess"
    :can-fullscreen="false"
    :destroy-on-close="true"
    width="68%"
  >
    <a-row>
      <a-col :span="10">
        <BasicTree
          :expandOnSearch="true"
          :search="true"
          :checkable="checkable"
          :check-strictly="true"
          :treeData="treeData"
          :showLine="true"
          :load-data="onLoadData"
          :class="`tree-info ${$style['receive-user']}`"
          placeholderSearch="请输入名称"
          ref="treeRef"
          :defaultExpandLevel="1"
          :checkedKeys="checkedKeys"
          :selectedKeys="selectedKeys"
          @check="handleCheckTree"
          @select="handleSelectTree"
          :height="500"
          :virtual-scroll-height="36"
          :virtual="true"
        >
          <template #title="item">
            <Tooltip
              class="!truncate text-15px w-3/5"
              :mouse-enter-delay="0.5"
            >
              <template #title>{{ item.title }}</template>
              {{ item.title }}
            </Tooltip>
            <div
              class="w-2/5"
              v-if="item.checkable && checkedKeys.includes(item.key)"
            >
              <a-radio-group
                v-model:value="selectType[item.key]"
                :options="options"
                :disabled="!!item.ifShowRadio"
                @click="e => handleRadio(item, e)"
              />
            </div>
          </template>
        </BasicTree>
      </a-col>
      <a-col
        :span="14"
        v-show="ifShowTable || ifHomeLaunch"
      >
        <BasicTable @register="registerTable" />
      </a-col>
    </a-row>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, unref, shallowRef } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { BasicTable, useTable } from '/@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { BasicTree, TreeItem, TreeActionType } from '@/components/Tree';
import { isArray, map, uniq, isEmpty, groupBy, includes, forEach, filter } from 'lodash-es';
import { unionNextLevel, unionUser } from '@/api';
import { useUserStore } from '@/store/modules/user';
import { RadioGroupProps, Tooltip } from 'ant-design-vue';
import { paged } from '@/api/union/cadre';
import { userList } from '@/api/system/userLabel';
import { watch } from 'vue';
import { list } from '@/api/system/userLabel';

const NZZDY = {
  NZ: 'builtIn',
  ZDY: 'custom',
};

const props = defineProps({
  checkable: { type: Boolean, default: true },
  ifHomeLaunch: { type: Boolean, default: false },
});

const emit = defineEmits(['register', 'success']);

const dictionary = useDictionary();

const userStore = useUserStore();

const TableApi = { GHGB: paged, ZDY: userList, NZ: userList, GHXX: unionUser };

const options: RadioGroupProps['options'] = [
  { label: '全选', value: 'all' },
  { label: '局部', value: 'alone' },
];

const record = ref<Recordable>();

let selectType = reactive<Recordable>({});

const treeRef = ref<Nullable<TreeActionType>>(null);

const treeData = shallowRef<TreeItem[]>([]);

const checkedKeys = ref<string[]>([]);

const selectedKeys = ref<string[]>([]);

const title = computed(() => {
  return `请选择接收人--类型为【${unref(record)?.type ? dictionary.getDictionaryMap.get(`mesUserType_${unref(record)?.type}`)?.dictName : ''}】`;
});

const ifShowTable = computed(() => {
  const selectedKey = selectedKeys.value?.[0];
  return (
    !isEmpty(selectedKey) &&
    checkedKeys.value.includes(selectedKey) &&
    selectType[selectedKey] === 'alone'
  );
});

const [
  registerTable,
  { reload, setProps, clearSelectedRowKeys, setSelectedRows, getSelectRows, setTableData },
] = useTable({
  rowKey: props.ifHomeLaunch ? 'cadreId' : 'userAccount',
  columns: [
    { dataIndex: 'souceId', title: '关联id', defaultHidden: true },
    { dataIndex: 'dataType', title: '数据类型', defaultHidden: true },
    { dataIndex: 'cadreId', title: '干部id', defaultHidden: true },
    { dataIndex: 'userName', title: '姓名' },
    { dataIndex: 'userAccount', title: '手机号' },
  ],
  showIndexColumn: false,
  formConfig: {
    labelWidth: 120,
    schemas: [
      {
        field: 'userName',
        component: 'Input',
        label: '姓名',
        colProps: { span: 9 },
        rulesMessageJoinLabel: true,
      },
      {
        field: 'userAccount',
        component: 'Input',
        label: '手机号',
        colProps: { span: 9 },
        rulesMessageJoinLabel: true,
      },
    ],
    autoSubmitOnEnter: true,
  },
  rowSelection: {
    type: 'checkbox',
  },
  clickToRowSelect: true,
  maxHeight: 390,
  useSearchForm: true,
  bordered: true,
  immediate: false,
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
  const ifLast = unref(record)?.type === data?.type;

  record.value = data;

  const { contact, contactType } = data;

  checkedKeys.value = [];

  selectedKeys.value = [];

  setTableData([]);
  clearSelectedRowKeys();

  forEach(Reflect.ownKeys(selectType), v => Reflect.deleteProperty(selectType, v));

  // 树结构
  if (includes(['GHGB', 'GHXX'], unref(record)?.type)) {
    //TODO 这个不分需要手动去查不在去走store
    treeData.value = [];
    const companyId = userStore.getUserInfo.companyId;

    // 首页推送过来的
    if (props.ifHomeLaunch) {
      setProps({ rowKey: 'cadreId' });

      selectedKeys.value = [unref(treeData)?.[0]?.id];
    }
    if (
      companyId === '6650f8e054af46e7a415be50597a99d5' &&
      treeData.value[0] &&
      !isEmpty(unref(treeData)?.[0]?.children)
    ) {
      treeData.value[0]['children'] = map(
        groupBy(unref(treeData)?.[0]?.children || [], v => v.unionCategory),
        (v, k) => {
          const name = dictionary.getDictionaryMap.get(`gonghuileibie_${k}`)?.dictName;
          return {
            title: name,
            unionName: name,
            checkable: false,
            key: `gonghuileibie_${k}`,
            children: v,
            id: `gonghuileibie_${k}`,
            selectable: false,
          };
        }
      );
    }
  } else {
    const { data } = await list({ pageSize: 0, labelType: NZZDY[unref(record)?.type] });

    treeData.value = map(data, v => ({
      title: v.labelName,
      key: v.labelCode,
      selectable: true,
      checkable: true,
      id: v.labelCode,
    }));
  }

  // 人
  if (!isEmpty(contact) && (ifLast || data.isUpdate)) {
    forEach(contact, v => {
      unref(checkedKeys).push(v.souceId);

      selectType[v.souceId] = 'alone';
    });
    setSelectedRows(contact);
  }

  // 树
  if (!isEmpty(contactType) && (ifLast || data.isUpdate)) {
    forEach(contactType, v => {
      selectType[v.souceId] = 'all';
      unref(checkedKeys).push(v.souceId);
    });
  }

  setModalProps({ confirmLoading: false });

  // 动态设置table
  setProps({
    api: TableApi[unref(record)?.type],
    searchInfo: {
      tenantChildFlag: undefined,
    },
    fetchSetting: {
      totalField: includes(['GHGB', 'GHXX'], unref(record)?.type) ? 'recordCount' : 'total',
    },
    beforeFetch(params) {
      switch (unref(record)?.type) {
        case 'ZDY':
        case 'NZ':
          params['nickName'] = params['userName'];
          params['phone'] = params['userAccount'];
          params['recordState'] = 'y';
          params['labelCode'] = unref(selectedKeys)[0];
          break;
        case 'GHGB':
          params['pi'] = params['pageNum'];
          params['ps'] = params['pageSize'];
          params['un'] = params['userName'];
          params['tel'] = params['userAccount'];
          params['uid'] = unref(selectedKeys)[0];
          break;
        case 'GHXX':
          params['pi'] = params['pageNum'];
          params['ps'] = params['pageSize'];
          params['key'] = params['userName'];
          params['p'] = params['userAccount'];
          params['uid'] = unref(selectedKeys)[0];

          break;
        default:
          break;
      }
      return params;
    },
    afterFetch(data) {
      switch (unref(record)?.type) {
        case 'ZDY':
        case 'NZ':
          return map(data, v => ({
            userAccount: v.phone,
            userName: v.nickName,
            souceId: v.labelCode,
          }));
        case 'GHGB':
          return map(data.data, v => ({
            userAccount: v.contractPhone,
            userName: v.cadreName,
            souceId: v.unionId,
            cadreId: v.cadreId,
          }));
        case 'GHXX':
          return map(data.data, v => ({
            userAccount: v.a0115,
            userName: v.a0100,
            souceId: v.a0137,
          }));
        default:
          break;
      }
    },
  });
});

// 优化数据处理函数
// 异步加载
function onLoadData(treeNode) {
  if (!includes(['GHGB', 'GHXX'], unref(record)?.type)) {
    return Promise.resolve();
  }

  const { id } = treeNode;

  return new Promise((resolve: (value?: unknown) => void) => {
    if (isArray(treeNode.children) && treeNode.children.length > 0) {
      resolve();
      return;
    }
    setTimeout(async () => {
      const asyncTreeAction: TreeActionType | null = unref(treeRef);
      if (asyncTreeAction) {
        await unionNextLevel({ unionId: id }).then(({ data }) => {
          const { data: originData } = data as { data: [] };
          let children: Recordable[] = map(originData, v => {
            return {
              id: v['id'],
              key: v['id'],
              unionName: v['c0100'],
              title: v['c0100'],
              areaName: v['c0102'],
              level: v['c0107'],
              provinceId: v['c0108'],
              cityId: v['c0109'],
              areaId: v['c0110'],
              ifDelete: v['c0114'],
              name: v['c0215'],
              phone: v['c0216'],
              unionCategory: v['c0217'],
              checkable: true,
            };
          });

          if (treeNode.dataRef) {
            asyncTreeAction.updateNodeByKey(treeNode.eventKey, { children });
            asyncTreeAction.setExpandedKeys(
              uniq([treeNode.eventKey, ...asyncTreeAction.getExpandedKeys()])
            );

            resolve();
          }
        });
      }

      resolve();
      return;
    }, 300);
  });
}

function handleRadio(item, event) {
  event.stopPropagation(); // 阻止事件冒泡
  const key = item.key || item.id;
  const newValue = event.target.value;

  // 更新选择类型
  selectType[key] = newValue;

  // 确保节点被选中
  if (!checkedKeys.value.includes(key)) {
    checkedKeys.value = [...checkedKeys.value, key];
  }

  // 更新选中节点
  selectedKeys.value = [key];
}

// 修改 handleCheckTree 函数
function handleCheckTree(rawVal, e) {
  const { node, checked: isChecked } = e;
  const key = node.key || node.id;

  // 保持原有选中状态，只更新当前节点
  const newCheckedKeys = [...checkedKeys.value];
  const index = newCheckedKeys.indexOf(key);

  if (isChecked) {
    // 选中节点
    if (index === -1) {
      newCheckedKeys.push(key);
      // 初始化选择类型为 null，这样会立即显示单选按钮组
      selectType[key] = null;
    }
  } else {
    // 取消选中节点
    if (index > -1) {
      newCheckedKeys.splice(index, 1);
      // 清除该节点的选择类型
      Reflect.has(selectType, key) && Reflect.deleteProperty(selectType, key);
    }
  }

  checkedKeys.value = newCheckedKeys;

  // 只在选中时更新 selectedKeys
  if (isChecked) {
    selectedKeys.value = [key];
  } else if (selectedKeys.value[0] === key) {
    // 如果取消选中的是当前选中节点，清空选中
    selectedKeys.value = [];
  }
}

// 修改 handleSelectTree 函数
function handleSelectTree(_, { node }) {
  const key = node.key || node.id;
  selectedKeys.value = [key];
}

// 提交
function handleSuccess() {
  const selects = getSelectRows();

  const allSelectId = map(
    filter(
      map(selectType, (v, k) => ({ souceId: k, val: v })),
      f => f.val === 'all'
    ),
    m => m.souceId
  );

  emit('success', {
    receiveUserDTOList: map(selects, v => ({ ...v, dataType: 'alone' })),
    typeParams: allSelectId,
    receiveType: unref(record)?.type,
  });

  closeModal();
}

// 修改 watch 函数
watch(selectedKeys, async () => {
  if (!(ifShowTable.value || props.ifHomeLaunch)) {
    return;
  }

  const currentKey = selectedKeys.value?.[0];
  if (!currentKey) return;

  await reload({
    searchInfo: {
      uid: currentKey,
      labelCode: currentKey,
    },
  });
});
</script>

<style lang="less" module>
.receive-user {
  :global {
    .ant-tree {
      overflow: auto;

      .ant-tree-treenode {
        margin-top: 2px;
        height: 36px;

        .ant-tree-title {
          @apply text-15px !h-full;
          display: flex;
          align-items: center;
        }

        svg {
          color: #184564;
          font-size: 14px;
        }

        .ant-tree-node-content-wrapper {
          min-height: 32px;
          line-height: 32px;
        }
      }

      .ant-tree-switcher {
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
</style>
