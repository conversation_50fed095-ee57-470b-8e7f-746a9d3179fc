<template>
  <div class="mb-8 text-center enter-x">
    <h2 class="text-3xl font-bold text-gray-800 mb-2">
      {{ getFormTitle }}
    </h2>
    <div class="flex justify-center">
      <div class="h-1 w-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
    </div>
    <p class="text-gray-500 mt-3 text-sm"> 欢迎回来，请输入您的登录信息 </p>
  </div>
</template>
<script lang="ts" setup>
import { computed, unref } from 'vue';
import { useI18n } from '@/hooks/web/useI18n';
import { LoginStateEnum, useLoginState } from './useLogin';

const { t } = useI18n();

const { getLoginState } = useLoginState();

const getFormTitle = computed(() => {
  const titleObj = {
    [LoginStateEnum.RESET_PASSWORD]: t('sys.login.forgetFormTitle'),
    [LoginStateEnum.LOGIN]: t('sys.login.signInFormTitle'),
    [LoginStateEnum.REGISTER]: t('sys.login.signUpFormTitle'),
    [LoginStateEnum.MOBILE]: t('sys.login.mobileSignInFormTitle'),
    [LoginStateEnum.QR_CODE]: t('sys.login.qrSignInFormTitle'),
  };
  return titleObj[unref(getLoginState)];
});
</script>
