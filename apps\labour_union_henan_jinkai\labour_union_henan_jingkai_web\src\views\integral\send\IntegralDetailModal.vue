<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="modalTitle"
    :canFullscreen="false"
    width="70%"
    :showOkBtn="false"
    :wrapClassName="$style['detail-modal']"
  >
    <!-- 发放信息概览 -->
    <div class="mb-4 p-4 bg-gray-50 rounded">
      <a-row :gutter="16">
        <a-col :span="6">
          <div class="text-sm text-gray-600">发放名称</div>
          <div class="font-medium">{{ distributionInfo.distributionName }}</div>
        </a-col>
        <a-col :span="6">
          <div class="text-sm text-gray-600">发放分值</div>
          <div class="font-medium text-green-600">+{{ distributionInfo.integralScore }}</div>
        </a-col>
        <a-col :span="6">
          <div class="text-sm text-gray-600">发放人数</div>
          <div class="font-medium">{{ distributionInfo.distributionCount }}</div>
        </a-col>
        <a-col :span="6">
          <div class="text-sm text-gray-600">成功/失败</div>
          <div class="font-medium">
            <a-tag color="success">{{ distributionInfo.successCount }}</a-tag>
            /
            <a-tag color="error">{{ distributionInfo.failCount }}</a-tag>
          </div>
        </a-col>
      </a-row>
      <a-row
        :gutter="16"
        class="mt-2"
      >
        <a-col :span="12">
          <div class="text-sm text-gray-600">发放时间</div>
          <div class="font-medium">{{ distributionInfo.createTime }}</div>
        </a-col>
        <a-col :span="12">
          <div class="text-sm text-gray-600">发放备注</div>
          <div class="font-medium">{{ distributionInfo.distributionRemark || '-' }}</div>
        </a-col>
      </a-row>
    </div>

    <!-- 明细列表 -->
    <a-tabs
      v-model:activeKey="activeTab"
      @change="onTabChange"
      :destroyInactiveTabPane="true"
    >
      <a-tab-pane
        key="SUCCESS"
        :forceRender="true"
        :tab="`成功明细 (${distributionInfo.successCount})`"
      >
        <BasicTable @register="registerSuccessTable" />
      </a-tab-pane>
      <a-tab-pane
        key="FAIL"
        :forceRender="true"
        :tab="`失败明细 (${distributionInfo.failCount})`"
      >
        <BasicTable @register="registerFailedTable" />
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicTable, useTable } from '@/components/Table';
import { detailColumns, detailSearchFormSchema, detailSuccessColumns } from './data';
import { distributionIntegralDetail, distributionIntegralRecordList } from '@/api/integral/send';

defineOptions({ name: 'IntegralDetailModal' });

const modalTitle = ref('积分发放明细');
const activeTab = ref('SUCCESS');
const distributionInfo = reactive({
  distributionName: '',
  integralScore: 0,
  distributionCount: 0,
  successCount: 0,
  failCount: 0,
  createTime: '',
  distributionRemark: '',
});

let currentDistributionId = ref();

// 成功明细表格
const [registerSuccessTable, { reload: reloadSuccessTable }] = useTable({
  api: distributionIntegralRecordList,
  columns: detailSuccessColumns,
  formConfig: {
    labelWidth: 80,
    schemas: detailSearchFormSchema,
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: { span: 6 },
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  maxHeight: 400,
  showIndexColumn: true,
  rowKey: 'autoId',
  immediate: false,
  beforeFetch: params => {
    return {
      ...params,
      distributionId: currentDistributionId,
      distributionStatus: 'SUCCESS',
    };
  },
});

// 失败明细表格
const [registerFailedTable, { reload: reloadFailedTable }] = useTable({
  api: distributionIntegralRecordList,
  columns: detailColumns,
  formConfig: {
    labelWidth: 80,
    schemas: detailSearchFormSchema,
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    actionColOptions: { span: 6 },
  },
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  maxHeight: 400,
  immediate: false,
  showIndexColumn: true,
  rowKey: 'autoId',
  beforeFetch: params => {
    return {
      ...params,
      distributionId: currentDistributionId,
      distributionStatus: 'FAIL',
    };
  },
});

// tab切换处理
const onTabChange = (key: string) => {
  activeTab.value = key;
};

const [registerModal, { setModalProps }] = useModalInner(async data => {
  setModalProps({ confirmLoading: false });

  activeTab.value = 'SUCCESS';

  if (data?.record) {
    currentDistributionId = data.record.distributionId;
    modalTitle.value = `积分发放明细 - ${data.record.distributionName}`;

    // 获取发放详情
    const detailResult = await distributionIntegralDetail({
      autoId: data.record.autoId,
    });

    if (detailResult.code === 200 && detailResult.data) {
      Object.assign(distributionInfo, detailResult.data);
    }
  }

  await nextTick(() => {
    setTimeout(() => {
      reloadSuccessTable();
      reloadFailedTable();
    }, 50);
  });
});
</script>

<style lang="less" module>
.detail-modal {
  :global {
    .ant-modal-body {
      max-height: 70vh;
      overflow: auto;
    }

    .ant-tag {
      margin: 0 2px;
    }
  }
}
</style>
