import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { list } from '@/api/system/userLabel';
import { RadioGroupChildOption } from 'ant-design-vue/lib/radio/Group';
import { includes } from 'lodash-es';
import CompanySelect from '@/views/components/company-select/index.vue';
import cascaderOptions, { DivisionUtil } from '@pansy/china-division';
import { YESNOEnum } from '@monorepo-yysz/enums';

const dictionary = useDictionary();

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '会员名',
      dataIndex: 'nickname',
      width: 120,
    },
    {
      title: '组织名',
      dataIndex: 'authCompanyName',
      width: 180,
    },

    {
      title: '证件号',
      dataIndex: 'identityNum',
      width: 120,
    },

    {
      title: '会员性别',
      dataIndex: 'xb',
      width: 100,
      customRender({ text }) {
        const name = text === 'man' ? '男' : '女';
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '联系电话',
      dataIndex: 'lxdh',
      width: 100,
    },
    {
      title: '现居地',
      dataIndex: 'xjd',
      width: 100,
    },
    // 会籍状态
    {
      title: '会籍状态',
      dataIndex: 'hjzt',
      width: 100,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`hjzt_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
    },
    // 户籍类型
    {
      title: '户籍类型',
      dataIndex: 'hjlx',
      width: 100,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`hjlx_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
      defaultHidden: true,
    },
    // 户籍
    {
      title: '户籍',
      dataIndex: 'hjd',
      width: 100,
      defaultHidden: true,
    },
    // 入会时间
    {
      title: '入会时间',
      dataIndex: 'companyJoinTime',
      width: 150,
      defaultHidden: true,
    },
    // 技能等级
    {
      title: '技能等级',
      dataIndex: 'jndj',
      width: 100,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`jndj_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
      defaultHidden: true,
    },
    // 新就业形态劳动者
    {
      title: '新就业形态劳动者',
      dataIndex: 'sfxjyxtldz',
      width: 150,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`YesOrNo_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
      defaultHidden: true,
    },
    // ejzylx
    {
      title: '新就业形态类型',
      dataIndex: 'ejzylx',
      width: 150,
      customRender: ({ text }) => {
        const name = dictionary.getDictionaryMap.get(`lhjyzw_${text}`)?.dictName || '';
        return <span title={name}>{name}</span>;
      },
      defaultHidden: true,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'nickname',
      label: '会员名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'xb',
      label: '会员性别',
      colProps: { span: 6 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '全部', value: undefined },
          { label: '男', value: 'man' },
          { label: '女', value: 'female' },
        ],
      },
    },
    {
      field: 'lxdh',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'identityNum',
      label: '身份证号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'memberNation',
      label: '民族',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('mz') || [],
      },
    },
    {
      field: 'zzmm',
      label: '政治面貌',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('zzmm') || [],
      },
    },

    {
      field: 'hjzt',
      label: '会籍状态',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('hjzt') || [],
      },
    },
    {
      field: 'jndj',
      label: '技能等级',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('jndj') || [],
      },
    },
    {
      field: 'sfxjyxtldz',
      label: '新就业形态劳动者',
      colProps: { span: 6 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '全部', value: undefined },
          ...((dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || []),
        ],
      },
    },
    {
      field: 'ejzylx',
      label: '新就业形态类型',
      colProps: { span: 6 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('lhjyzw') || [],
      },
    },
    {
      field: 'xl',
      label: '学历',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('xlcc') || [],
      },
    },
    {
      field: 'tenantChildFlag',
      component: 'Select',
      label: '数据范围',
      colProps: { span: 4 },
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '本级', value: 0 },
          { label: '下一级', value: 1 },
          { label: '所有下级', value: 2 },
        ],
      }
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const divisionUtil = new DivisionUtil(cascaderOptions);

  const cityData = divisionUtil.getSourceData();

  return [
    {
      field: 'nickname',
      label: '会员名',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyId',
      label: '认证组织',
      show: false,
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'authCompanyName',
      label: '认证组织',
      colProps: { span: 12 },
      component: 'ApiTreeSelect',
      required: true,
      rest: true,
      rulesMessageJoinLabel: true,
      render: ({ model, values }) => {
        return (
          <CompanySelect
            value={values.authCompanyName}
            onChange={(v: { companyId: string; companyName: string; record: Recordable }) => {
              const { companyId, companyName } = v;

              model['companyId'] = companyId;
              model['authCompanyName'] = companyName;
            }}
          />
        );
      },
    },
    {
      field: 'companyJoinTime',
      label: '入会时间',
      colProps: { span: 12 },
      required: true,
      component: 'DatePicker',
      className: '!w-full',
      rulesMessageJoinLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择入会时间',
      },
    },
    {
      field: 'memberArea',
      label: '所属地区',
      colProps: { span: 12 },
      component: 'Cascader',
      required: true,
      rulesMessageJoinLabel: true,
      defaultValue: ['410000', '410100', '410171'],
      componentProps: function () {
        return {
          options: cityData,
        };
      },
    },
    {
      field: 'memberNation',
      label: '民族',
      colProps: { span: 12 },
      component: 'Select',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('mz') || [],
      },
    },
    {
      field: 'identityType',
      label: '证件类型',
      colProps: { span: 12 },
      required: true,
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'sfz',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('zjlx') || [],
      },
    },
    {
      field: 'identityNum',
      label: '证件号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      ifShow: ({ values }) => !!values.identityType,
      rulesMessageJoinLabel: true,
    },
    {
      field: 'ifIdentityExpired',
      label: '证件是否长期',
      colProps: { span: 12 },
      component: 'RadioGroup',
      required: true,
      defaultValue: YESNOEnum.NO,
      rulesMessageJoinLabel: true,
      componentProps: {
        options: (dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || [],
      },
    },
    {
      field: 'identityExpired',
      label: '证件截止日期',
      colProps: { span: 12 },
      component: 'DatePicker',
      required: true,
      ifShow: ({ values }) => values.ifIdentityExpired === YESNOEnum.NO,
      rulesMessageJoinLabel: true,
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
    },
    {
      field: 'xb',
      label: '会员性别',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'man',
      required: true,
      componentProps: {
        options: [
          { label: '男', value: 'man' },
          { label: '女', value: 'female' },
        ],
      },
    },
    {
      field: 'lxdh',
      label: '联系电话',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
    },

    {
      field: 'hjlx',
      label: '户籍类型',
      colProps: { span: 12 },
      component: 'Select',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('hjlx') || [],
      },
    },
    {
      field: 'hj',
      label: '户籍',
      colProps: { span: 12 },
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'hjxxdz',
      label: '户籍详细地址',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'xjd',
      label: '现居地',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'zzmm',
      label: '政治面貌',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: '13',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('zzmm') || [],
      },
    },
    {
      field: 'jyzt',
      label: '就业状态',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: YESNOEnum.YES,
      componentProps: {
        options: (dictionary.getDictionaryOpt.get('YesOrNo') as RadioGroupChildOption[]) || [],
      },
    },
    {
      field: 'xl',
      label: '学历',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'dxbk',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('xlcc') || [],
      },
    },
    {
      field: 'xw',
      label: '学位',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'w',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('xwdj') || [],
      },
    },
    {
      field: 'jndj',
      label: '技能等级',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'w',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('jndj') || [],
      },
    },
    {
      field: 'hyzk',
      label: '婚姻状况',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'wz',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('hyzk') || [],
      },
    },
    {
      field: 'qtlxfs',
      label: '其他联系方式',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'gzzw',
      label: '工作职务',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'zcdj',
      label: '职称等级',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'w',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('zcdj') || [],
      },
    },
    {
      field: 'hjzt',
      label: '会籍状态',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'zc',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('hjzt') || [],
      },
    },
    {
      field: 'hjbhsj',
      label: '会籍变化时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => values.hjzt !== 'zc',
      componentProps: {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '请选择会籍变化时间',
      },
    },
    {
      field: 'hjbhyya',
      label: '会籍变化原由',
      colProps: { span: 12 },
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => values.hjzt !== 'zc',
      componentProps: {
        rows: 3,
        placeholder: '请输入会籍变化原由',
      },
    },
    {
      field: 'hjbhyyb',
      label: '会籍变化原因',
      colProps: { span: 12 },
      ifShow: ({ values }) => values.hjzt !== 'zc',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        rows: 3,
        placeholder: '请输入会籍变化原因',
      },
    },
    {
      field: 'ghgbzw',
      label: '工会干部职务',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'wzw',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('ghgbzw') || [],
      },
    },
    {
      field: 'ghjczw',
      label: '工会基层职务',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => includes(['shhghgz', 'jcghgbzz', 'jcghgbjz'], values.yjzylx),
      defaultValue: 'zgdb',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('ghjczw') || [],
      },
    },
    {
      field: 'sflm',
      label: '是否劳模',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: false,
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      field: 'sfcjzghzbz',
      label: '是否参加职工互助保障',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      labelWidth: 180,
      defaultValue: false,
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      field: 'sfknzg',
      label: '是否困难职工',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: false,
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      field: 'sfyczfc',
      label: '是否有城镇房产',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      defaultValue: false,
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      field: 'yjzylx',
      label: '一级职业类型',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      defaultValue: 'zyjs',
      componentProps: ({ formModel }) => {
        return {
          options: dictionary.getDictionaryOpt.get('zyfl') || [],
          onChange(value) {
            formModel['sfxjyxtldz'] = value === 'lhjy';
            formModel['xjyxtldz'] = value === 'lhjy' ? 'qt' : '';
          },
        };
      },
    },
    {
      field: 'sfxjyxtldz',
      label: '是否新就业形态劳动者',
      colProps: { span: 12 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      labelWidth: 180,
      defaultValue: false,
      show: ({ values }) => values.yjzylx === 'lhjy',
      dynamicDisabled: true,
      componentProps: {
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
    },
    {
      field: 'xjyxtldz',
      label: '新就业形态',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => !!values.sfxjyxtldz,
      defaultValue: 'qt',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('lhjyzw') || [],
      },
    },
    // {
    //   field: 'ejzylx',
    //   label: '二级职业类型',
    //   colProps: { span: 12 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   ifShow: ({ values }) => values.yjzylx === 'lhjy',
    //   defaultValue: 'qt',
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get('lhjyzw') || [],
    //   },
    // },

    {
      field: 'bzxx',
      label: '备注信息',
      component: 'InputTextArea',
      rulesMessageJoinLabel: true,
      componentProps: {
        rows: 3,
        placeholder: '请输入备注信息',
      },
    },
  ];
};

//标签管理弹框配置
export const labelUpdateModalForm = (): FormSchema[] => {
  return [
    {
      field: 'builtUserLabelList',
      label: '内置标签',
      required: false,
      itemProps: {
        autoLink: true,
      },
      dynamicDisabled: true,
      component: 'ApiSelect',
      colProps: { span: 24 },
      componentProps: ({}) => {
        return {
          mode: 'multiple',
          api: list,
          params: {
            pageSize: 0,
            labelType: 'builtIn',
          },
          resultField: 'data',
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'labelName', value: 'labelCode' },
          placeholder: '暂未获得',
        };
      },
    },
    {
      field: 'customUserLabelList',
      label: '自定义标签',
      required: false,
      itemProps: {
        autoLink: true,
      },
      component: 'ApiSelect',
      colProps: { span: 24 },
      componentProps: ({}) => {
        return {
          mode: 'multiple',
          api: list,
          params: {
            pageSize: 0,
            labelType: 'custom',
          },
          resultField: 'data',
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'labelName', value: 'labelCode' },
          placeholder: '请选择标签',
        };
      },
    },
    {
      field: 'id',
      label: '用户id',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      ifShow: false,
    },
    {
      field: 'a0115',
      label: '手机号码',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      ifShow: false,
    },
  ];
};
