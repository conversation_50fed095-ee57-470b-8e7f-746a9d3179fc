import { validatePhone } from '@monorepo-yysz/utils';
import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import CompanySelect from '@/views/components/company-select/index.vue';
import { list } from '@/api/system/dept';
import { useUserStore } from '@/store/modules/user';
import { getRoleList } from '@/api/system/role';

const dictionary = useDictionary();

export const getColumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'nickname',
    },
    {
      title: '联系电话',
      dataIndex: 'account',
    },
    {
      title: '身份证号',
      dataIndex: 'gbsfzh',
    },
    {
      title: '性别',
      dataIndex: 'gbxb',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`xb_${record.gbxb}`)?.dictName;
        return name || record.gbxb;
      },
    },
    {
      title: '民族',
      dataIndex: 'gbmz',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`mz_${record.gbmz}`)?.dictName;
        return name || record.gbmz;
      },
    },
    {
      title: '学历',
      dataIndex: 'gbxl',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`xlcc_${record.gbxl}`)?.dictName;
        return name || record.gbxl;
      },
    },
    {
      title: '部门名称',
      dataIndex: 'deptName',
    },
    {
      title: '职务名称',
      dataIndex: 'postName',
    },
    {
      title: '政治面貌',
      dataIndex: 'gbzzmm',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`zzmm_${record.gbzzmm}`)?.dictName;
        return name || record.gbzzmm;
      },
    },
  ];
};

export const pagedColumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'nickname',
    },
    {
      title: '联系电话',
      dataIndex: 'account',
    },
    {
      title: '身份证号',
      dataIndex: 'gbsfzh',
    },
    {
      title: '性别',
      dataIndex: 'gbxb',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`xb_${record.gbxb}`)?.dictName;
        return name || record.gbxb;
      },
    },
    {
      title: '民族',
      dataIndex: 'gbmz',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`mz_${record.gbmz}`)?.dictName;
        return name || record.gbmz;
      },
    },
    {
      title: '学历',
      dataIndex: 'gbxl',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`xlcc_${record.gbxl}`)?.dictName;
        return name || record.gbxl;
      },
    },
    {
      title: '部门名称',
      dataIndex: 'deptName',
    },
    {
      title: '职务名称',
      dataIndex: 'postName',
    },
    {
      title: '政治面貌',
      dataIndex: 'gbzzmm',
      customRender: ({ record }) => {
        const name = dictionary.getDictionaryMap.get(`zzmm_${record.gbzzmm}`)?.dictName;
        return name || record.gbzzmm;
      },
    },
  ];
};

export const pagedFormSchema = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'nickname',
      label: '姓名',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },

    {
      field: 'account',
      label: '联系电话',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'gbsfzh',
      label: '身份证号',
      colProps: { span: 6 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },

    {
      field: 'gbmz',
      label: '民族',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('mz') || [],
      },
    },

    {
      field: 'gbxl',
      label: '学历',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('xlcc'),
      },
    },
    {
      field: 'gbzzmm',
      label: '政治面貌',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('zzmm'),
      },
    },
    {
      field: 'gbxb',
      label: '性别',
      colProps: { span: 6 },
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '全部', value: undefined },
          ...((dictionary.getDictionaryOpt.get('xb') as RadioGroupChildOption[]) || []),
        ],
      },
    },
    {
      field: 'tenantChildFlag',
      component: 'Select',
      label: '数据范围',
      colProps: { span: 4 },
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '本级', value: 0 },
          { label: '下一级', value: 1 },
          { label: '所有下级', value: 2 },
        ],
      }
    },
  ];
};

export const modalFormItem = (): FormSchema[] => {
  const userStore = useUserStore();
  return [
    {
      field: 'nickname',
      component: 'Input',
      label: '姓名',
      required: true,
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
    },
    {
      field: 'account',
      component: 'Input',
      label: '联系电话',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      rules: [{ required: true, validator: validatePhone }],
    },
    {
      field: 'companyId',
      label: '所属工会',
      show: false,
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'companyName',
      label: '所属工会',
      colProps: { span: 12 },
      component: 'ApiTreeSelect',
      required: true,
      rest: true,
      rulesMessageJoinLabel: true,
      render: ({ model, values }) => {
        return (
          <CompanySelect
            value={values.companyName}
            onChange={(v: { companyId: string; companyName: string; record: Recordable }) => {
              const { companyId, companyName } = v;

              model['companyId'] = companyId;
              model['companyName'] = companyName;
            }}
          />
        );
      },
    },
    {
      field: 'gbsfzh',
      component: 'Input',
      label: '身份证号',
      required: true,
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
    },
    {
      field: 'gbxb',
      component: 'RadioGroup',
      label: '性别',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      required: true,
      defaultValue: '1',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('xb') as RadioGroupChildOption[],
      },
    },
    {
      field: 'gbmz',
      component: 'Select',
      label: '民族',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      defaultValue: 'hanzu',
      required: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('mz'),
      },
    },
    {
      field: 'gbxl',
      component: 'Select',
      label: '学历',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      required: true,
      defaultValue: 'dxbk',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('xlcc'),
      },
    },
    {
      field: 'gbzzmm',
      component: 'Select',
      label: '政治面貌',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      required: true,
      defaultValue: 'qz',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('zzmm'),
      },
    },
    {
      field: 'deptId',
      label: '部门名称',
      component: 'ApiTreeSelect',
      colProps: {
        span: 12,
      },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          api: list,
          fieldNames: {
            label: 'deptName',
            value: 'deptId',
          },
          resultField: 'data',
          params: {
            companyId: formModel['companyId']
              ? formModel['companyId']
              : userStore.getUserInfo.companyId,
          },
          alwaysLoad: true,
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.deptName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
        };
      },
    },
    {
      field: 'postId',
      label: '职务名称',
      rulesMessageJoinLabel: true,
      colProps: {
        span: 12,
      },
      component: 'ApiTreeSelect',
      required: false,
      componentProps({}) {
        return {
          api: getRoleList,
          resultField: 'data',
          fieldNames: {
            label: 'roleName',
            value: 'roleId',
          },
          params: {
            companyId: userStore.getUserInfo.companyId,
            pageSize: 999,
          },
          alwaysLoad: true,
          showSearch: true,
          treeDefaultExpandAll: true,
          filterTreeNode(input: string, option: any) {
            return option.roleName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          getPopupContainer: () => document.body,
        };
      },
    },
  ];
};
