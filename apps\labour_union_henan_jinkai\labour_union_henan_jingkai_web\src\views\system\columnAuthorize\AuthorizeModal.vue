<template>
  <BasicModal
    @register="registerModalInner"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <div>
      <BasicTree
        v-bind="$attrs"
        :checkable="true"
        :search="true"
        :toolbar="true"
        :treeData="treeData"
        :class="$style['column--tree']"
        :autoExpandParent="true"
        :selectable="false"
        :field-names="{ title: 'categoryName', key: 'autoId', children: 'children' }"
        v-model:checkedKeys="checkedKeys"
        v-model:expandedKeys="expanded"
        @expand="handleExpand"
        @check="handleCheck"
      />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { filter, join, map, split, toNumber } from 'lodash-es';
import { BasicTree, TreeItem, KeyType } from '@/components/Tree';
import { listTreeBind } from '@/api/category';
import { TreeDataItem } from 'ant-design-vue/es/tree/Tree';
import { getNode } from '@monorepo-yysz/utils';
import { useUserStore } from '@/store/modules/user';

defineOptions({ name: 'AuthorizeModal' });

const userStore = useUserStore();

const emit = defineEmits(['success', 'register']);

const columnList = ref<Recordable[]>([]);

const name = ref<string>('');

const autoId = ref<number>();

const title = computed(() => {
  return `${unref(name)}--绑定栏目`;
});

const checkedKeys = ref<KeyType[]>([]);

const expanded = ref<KeyType[]>([]);

const treeData = ref<TreeItem[]>([]);

const allData = ref<TreeDataItem[]>([]);

const record = ref<Recordable>();

const halfCheckedKeys = ref<KeyType[]>([]);

const [registerModalInner, { setModalProps }] = useModalInner(async data => {
  checkedKeys.value = [];
  expanded.value = [];
  columnList.value = [];
  treeData.value = [];
  allData.value = [];

  record.value = data.record;

  if (data.record) {
    treeData.value = (await listTreeBind({
      tenantChildFlag: '6650f8e054af46e7a415be50597a99d5' === userStore.getUserInfo.companyId,
    })) as TreeItem[];

    allData.value = getNode(unref(treeData));

    name.value = data.record.roleName;
    autoId.value = data.record.autoId;
    checkedKeys.value = map(data.columnList, v => toNumber(v));
    expanded.value = data.columnList;
    columnList.value = data.columnList;
    halfCheckedKeys.value = data.record.halfSelectedCategoryIds
      ? map(split(data.record.halfSelectedCategoryIds, ','), v => Number(v))
      : [];
  }

  setModalProps({
    confirmLoading: false,
  });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });
    emit('success', {
      ...unref(record),
      categoryIds: unref(checkedKeys).join(','),
      roleId: unref(autoId),
      halfSelectedCategoryIds: join(unref(halfCheckedKeys), ','),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

function handleExpand(expandedKeys) {
  expanded.value = filter(expandedKeys, v => !unref(columnList).includes(v));
}

function handleCheck(_, { checkedNodes, halfCheckedKeys: halfCheckeds }) {
  // filter(checkedNodes, v => !v.children),
  checkedKeys.value = map(checkedNodes, v => v.autoId) || [];
  halfCheckedKeys.value = halfCheckeds;
}
</script>

<style lang="less" module>
.column--tree {
  :global {
    .ant-tree-checkbox-inner {
      border-color: rgb(37, 37, 37);
    }
  }
}
</style>
