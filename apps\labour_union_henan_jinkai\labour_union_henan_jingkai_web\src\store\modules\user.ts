import { Recordable } from '@monorepo-yysz/types';
import type { CompanyInfo, LoginInfo, UserInfo } from '#/store';
import type { ErrorMessageMode } from '#/axios';
import { defineStore } from 'pinia';
import { store } from '@/store';
import { RoleEnum } from '@monorepo-yysz/enums';
import { PageEnum } from '@/enums/pageEnum';
import {
  COMPANY_INFO_KEY,
  ROLES_KEY,
  TOKEN_KEY,
  UNION_TOKEN_KEY,
  USER_INFO_KEY,
  LOGIN_INFO_KEY,
} from '@monorepo-yysz/enums';
import { getAuthCache, setAuthCache } from '@/utils/auth';
import { GetUserInfoModel, LoginParams } from '@/api/sys/model/userModel';
import { doLogout, getUserInfo, loginApi } from '@/api/sys/user';
import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@monorepo-yysz/hooks';
import { router } from '@/router';
import { LocationQueryValue, RouteRecordRaw } from 'vue-router';
import { h } from 'vue';
import { Persistent } from '@/utils/cache/persistent';
import { useGlobSetting } from '@/hooks/setting';
import { asyncAddWebVisitSummary, getPrefix } from '@/api';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { forEach, isArray, isString } from 'lodash-es';
import { PAGE_NOT_FOUND_ROUTE } from '@/router/routes/basic';
import { usePermissionStore } from './permission';

interface UserState {
  userInfo: Nullable<UserInfo>;
  loginInfo: Nullable<LoginInfo>;
  companyInfo?: Nullable<CompanyInfo>;
  token?: string;
  roleList: RoleEnum[];
  sessionTimeout?: boolean;
  lastUpdateTime: number;
  prefix?: string;
  viewPrefix?: string;
  majorToken?: string;
  menuBadge: Recordable;
  badgeSse: Nullable<EventSourcePolyfill>;
}

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): UserState => ({
    // user info
    userInfo: null,
    loginInfo: null,
    // token
    token: undefined,
    // roleList
    roleList: [],
    // Whether the login expired
    sessionTimeout: false,
    // Last fetch time
    lastUpdateTime: 0,
    //前缀
    prefix: '',
    //前缀
    viewPrefix: '',
    companyInfo: null,
    majorToken: '',
    menuBadge: {},
    badgeSse: null,
  }),
  getters: {
    getCompanyInfo(state): CompanyInfo {
      return state.companyInfo || getAuthCache<CompanyInfo>(COMPANY_INFO_KEY) || {};
    },
    getUserInfo(state): UserInfo {
      return state.userInfo || getAuthCache<UserInfo>(USER_INFO_KEY) || {};
    },
    getLoginInfo(state): LoginInfo {
      return state.loginInfo || getAuthCache<LoginInfo>(LOGIN_INFO_KEY) || {};
    },
    getToken(state): string {
      return state.token || getAuthCache<string>(TOKEN_KEY);
    },
    getRoleList(state): RoleEnum[] {
      return state.roleList.length > 0 ? state.roleList : getAuthCache<RoleEnum[]>(ROLES_KEY);
    },
    getSessionTimeout(state): boolean {
      return !!state.sessionTimeout;
    },
    getLastUpdateTime(state): number {
      return state.lastUpdateTime;
    },
    getPrefix(state): string {
      return state.prefix || '';
    },
    getViewPrefix(state): string {
      return state.viewPrefix || '';
    },
    getMajorToken(state): string {
      return state.majorToken || getAuthCache<string>(UNION_TOKEN_KEY);
    },
    getMenuBadge(state): Nullable<Recordable> {
      return state.menuBadge;
    },
  },
  actions: {
    setMajorToken(info: string | undefined) {
      this.majorToken = info ? info : '';
      setAuthCache(UNION_TOKEN_KEY, info);
    },
    setToken(info: string | undefined) {
      this.token = info ? info : ''; // for null or undefined value
      setAuthCache(TOKEN_KEY, info);
    },
    setRoleList(roleList: RoleEnum[]) {
      this.roleList = roleList;
      setAuthCache(ROLES_KEY, roleList);
    },
    setUserInfo(info: UserInfo | null) {
      this.userInfo = info;
      this.lastUpdateTime = new Date().getTime();
      setAuthCache(USER_INFO_KEY, info);
    },
    setLoginInfo(info: LoginInfo | null) {
      this.loginInfo = info;
      setAuthCache(LOGIN_INFO_KEY, info);
    },
    setCompanyInfo(info: CompanyInfo | null) {
      this.companyInfo = info;
      setAuthCache(COMPANY_INFO_KEY, info);
    },
    setSessionTimeout(flag: boolean) {
      this.sessionTimeout = flag;
    },
    setAsyncAddWebVisitSummary() {
      asyncAddWebVisitSummary({ systemTypeCode: 'manage' });
    },
    resetState() {
      this.userInfo = null;
      this.token = '';
      this.roleList = [];
      this.sessionTimeout = false;
      this.badgeSse?.close();
      this.badgeSse = null;
      this.menuBadge = {};

      Persistent.clearAll(true);
    },
    async setPrefix() {
      const { visitPrefix } = await getPrefix();
      const { bucket_name } = useGlobSetting();
      this.prefix = `${visitPrefix}/${bucket_name}`;
    },
    /**
     * @description: login
     */
    async login(
      params: LoginParams & {
        goHome?: boolean;
        mode?: ErrorMessageMode;
        account?: string;
        token?: LocationQueryValue;
        device?: string;
        loginType?: string;
        verifyCodeId?: string;
        verifyCode?: string;
        authSystem?: string;
      }
    ): Promise<GetUserInfoModel | null> {
      try {
        const { ifLogin } = useGlobSetting();

        const { goHome = true, token } = params;
        let t = token as string;

        if (ifLogin !== '1') {
          const loginInfo = await loginApi({ ...params }, params.mode);
          t = loginInfo.saToken;
          this.setLoginInfo(loginInfo);
          this.setToken(t);
        }
        // save token
        this.setMajorToken(t);
        return this.afterLoginAction(goHome);
      } catch (error) {
        return Promise.reject(error);
      }
    },
    async afterLoginAction(goHome?: boolean): Promise<GetUserInfoModel | null> {
      if (!this.getToken) return null;
      // get user info
      const userInfo = await this.getUserInfoAction();

      const sessionTimeout = this.sessionTimeout;
      if (sessionTimeout) {
        this.setSessionTimeout(false);
      } else {
        const permissionStore = usePermissionStore();

        // 动态路由加载（首次）
        if (!permissionStore.isDynamicAddedRoute) {
          const routes = await permissionStore.buildRoutesAction();
          [...routes, PAGE_NOT_FOUND_ROUTE].forEach(route => {
            router.addRoute(route as unknown as RouteRecordRaw);
          });
          // 记录动态路由加载完成
          permissionStore.setDynamicAddedRoute(true);
        }

        goHome && (await router.replace(userInfo?.homePath || PageEnum.BASE_HOME));
      }
      return userInfo;
    },
    async getUserInfoAction(): Promise<UserInfo | null> {
      if (!this.getToken) return null;

      const userInfo = await getUserInfo({ openId: this.getLoginInfo?.openId });
      if (!this.loginInfo) {
        this.setLoginInfo(this.getLoginInfo);
      }

      this.setUserInfo(userInfo);
      // await this.setMenuBadge(userInfo);
      return userInfo;
    },
    /**
     * @description: logout
     */
    async logout(goLogin = false) {
      const { main_login, ifLogin } = useGlobSetting();

      if (this.getToken) {
        try {
          await doLogout();
        } catch {
          console.log('注销Token失败');
        }
      }
      this.resetState();
      setTimeout(() => {
        if (ifLogin === '1') {
          goLogin && window.open(main_login, '_self'); //工会登录页
        } else {
          if (goLogin) {
            // 直接回登陆页
            router.replace(PageEnum.BASE_LOGIN);
          } else {
            // 回登陆页带上当前路由地址
            router.replace({
              path: PageEnum.BASE_LOGIN,
              query: {
                redirect: encodeURIComponent(router.currentRoute.value.fullPath),
              },
            });
          }
        }
      }, 1300);
    },

    /**
     * @description: Confirm before logging out
     */
    confirmLoginOut() {
      const { createConfirm } = useMessage();
      const { t } = useI18n();
      createConfirm({
        iconType: 'warning',
        title: () => h('span', t('sys.app.logoutTip')),
        content: () => h('span', t('sys.app.logoutMessage')),
        onOk: async () => {
          // 主动登出，不带redirect地址
          await this.logout(true);
        },
      });
    },

    /**
     * @description: logout自动
     */
    async dymLogout(goLogin = false) {
      const { main_login, ifLogin } = useGlobSetting();

      this.resetState();
      if (goLogin) {
        if (ifLogin === '1') {
          window.open(main_login, '_self'); //工会登录页
        } else {
          router.push(PageEnum.BASE_LOGIN);
        }
      }
    },
    /**
     * @description: 设置菜单标徽
     */
    async setMenuBadge(userInfo) {
      if (userInfo?.companyId || userInfo?.companyId === 0) {
        const { apisse } = useGlobSetting();
        const badgeSse = new EventSourcePolyfill(
          `${apisse}/h5/h5Business/connectCountMenuUnhandled/${userInfo.companyId}`,
          {
            headers: {
              token: this.token,
            },
            heartbeatTimeout: 5 * 60 * 1000,
          }
        );

        // 监听消息事件
        badgeSse.onmessage = event => {
          // console.log('监听开始:', event.data);
          if (event.data && isString(event.data)) {
            const result = JSON.parse(event.data);

            isArray(result) &&
              forEach(result, (v: Recordable) => {
                this.menuBadge[v.name] = v.unhandledCount;
              });
          }
        };

        // 监听打开事件
        badgeSse.onopen = () => {
          console.log('链接建立成功');
          this.badgeSse?.close();
          this.badgeSse = badgeSse;
          this.menuBadge = {};
        };

        // 监听错误事件
        badgeSse.onerror = error => {
          console.error('监听错误:', error);
          this.badgeSse?.close();
          this.badgeSse = null;
          this.setMenuBadge(userInfo);
        };
      }
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
