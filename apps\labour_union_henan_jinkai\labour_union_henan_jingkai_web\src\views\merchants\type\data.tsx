import { BasicColumn, FormSchema } from '/@/components/Table';

export const columns = (): BasicColumn[] => {
  return [
    {
      title: '序号',
      dataIndex: 'sortNumber',
    },
    {
      title: '类型名称',
      dataIndex: 'typeName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      customRender: ({ text }) => {
        return text? <span>{text}</span> : '--';
      },
    },
  ];
};

//顶部搜索条件
export const checkFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'typeName',
      label: '类型名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 6 },
    },
    
  ];
};

// 表单
export const modalFormItem = (disabled: boolean): FormSchema[] => {
  return [
  {
      field: 'typeName',
      label: '类型名称',
      component: 'Input',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      componentProps: {
        showCount: true,
        maxlength: 6,
      },
      required: true
    },
    {
      field: 'sortNumber',
      label: '序号',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      colProps: { span: 24 },
      required: true
    },
    
   
  ];
};
