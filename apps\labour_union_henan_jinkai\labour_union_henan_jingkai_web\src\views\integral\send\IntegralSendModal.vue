<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    @ok="handleSubmit"
    :confirmLoading="confirmLoading"
  >
    <BasicForm @register="registerForm" />

    <!-- 文件上传区域 -->
    <div
      v-if="showFileUpload"
      class="mt-4"
    >
      <a-divider>批量导入</a-divider>
      <a-upload
        v-model:file-list="fileList"
        :before-upload="beforeUpload"
        @remove="handleRemove"
        accept=".xlsx,.xls,.csv"
        :max-count="1"
      >
        <a-button type="primary">
          <upload-outlined />
          选择文件
        </a-button>
      </a-upload>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { BasicModal, useModalInner } from '@/components/Modal';
import { BasicForm, useForm } from '@/components/Form';
import { integralFormSchema } from './data';
import { distributionIntegral, uploadPhones } from '@/api/integral/send';
import { useMessage } from '@monorepo-yysz/hooks';
import { UploadOutlined } from '@ant-design/icons-vue';
import type { UploadFile } from 'ant-design-vue';
import { includes, isArray, join, split } from 'lodash-es';

defineOptions({ name: 'IntegralSendModal' });

const emit = defineEmits(['success', 'register']);

const { createMessage } = useMessage();
const confirmLoading = ref(false);
const fileList = ref<UploadFile[]>([]);

// 处理发放类型变化
const handleDistributionTypeChange = (value: string) => {
  distributionType.value = value;
};

const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: integralFormSchema(handleDistributionTypeChange),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
  resetFields();

  // 重置发放类型
  distributionType.value = 'manualInput';

  if (data?.record) {
    // 编辑模式
    setFieldsValue({
      ...data.record,
      directionalCode: includes(data.record.directionalCode, ',')
        ? split(data.record.directionalCode, ',')
        : [data.record.directionalCode],
    });
    distributionType.value = data.record.distributionType || 'manualInput';
  }

  setModalProps({ confirmLoading: false });
});

const getTitle = computed(() => '积分发放');

// 使用响应式变量跟踪发放类型
const distributionType = ref('');
const showFileUpload = computed(() => distributionType.value === 'dataImport');

// 文件上传前处理
const beforeUpload = async (file: File) => {
  const isValidType = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv',
  ].includes(file.type || '');

  if (!isValidType) {
    createMessage.error('只能上传 Excel 或 CSV 文件！');
    return false;
  }

  const isLt10M = (file.size || 0) / 1024 / 1024 < 10;
  if (!isLt10M) {
    createMessage.error('文件大小不能超过 10MB！');
    return false;
  }

  // 如果是导入类型，需要先上传文件获取手机号码

  try {
    const result = await uploadPhones({ file });

    if (result.data.code === 200) {
      await setFieldsValue({ phones: result.data.data });
    } else {
      createMessage.error(result.data.message || '文件上传失败');
      return false;
    }
  } catch (error) {
    createMessage.error('文件上传失败');
    return null;
  }

  return false; // 阻止自动上传
};

// 移除文件
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};

// 处理文件上传
const handleFileUpload = async () => {
  if (fileList.value.length === 0) {
    createMessage.error('请选择要上传的文件');
    return null;
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    const values = await validate();
    confirmLoading.value = true;

    // 处理手机号码
    let phones = values.phones;

    if (values.distributionType === 'manualInput') {
      // 手工输入，处理手机号码格式
      phones = phones.replace(/[\n\r]/g, ',').replace(/[，]/g, ',');
      // 去除空格和重复的逗号
      phones = phones.replace(/\s/g, '').replace(/,+/g, ',').replace(/^,|,$/g, '');
    }

    const params: any = {
      distributionName: values.distributionName,
      distributionType: values.distributionType,
      phones: phones,
      operateType: values.operateType,
      integralScore: values.integralScore,
      distributionRemark: values.distributionRemark,
    };

    // 如果是定向群体类型，添加定向群体信息
    if (values.distributionType === 'appointGroup') {
      params.directionalCode = isArray(values.directionalCode)
        ? join(values.directionalCode, ',')
        : values.directionalCode;
      params.directionalName = values.directionalName;
    }

    const result = await distributionIntegral(params);

    if (result.code === 200) {
      createMessage.success('积分发放成功');
      closeModal();
      emit('success');
    } else {
      createMessage.error(result.message || '积分发放失败');
    }
  } catch (error) {
    console.error('积分发放失败:', error);
    createMessage.error('积分发放失败');
  } finally {
    confirmLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-upload-list) {
  margin-top: 8px;
}
</style>
