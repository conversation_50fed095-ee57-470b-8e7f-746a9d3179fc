<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleClick" auth='/merchants/type/add'>新增分类</a-button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="[
            {
              icon: 'fa6-solid:pen-to-square',
              label: '编辑',
              type: 'primary',
              onClick: handleView.bind(null, record, false),
              auth:'/merchants/type/edit'
            },
            {
              icon: 'fluent:delete-20-filled',
              label: '删除',
              type: 'primary',
              danger: true,
              onClick: handleDelete.bind(null, record),
              auth:'/merchants/type/delete'
            },
            ]" />
        </template>
      </template>
    </BasicTable>
    <handleModal @register="registerModal" @success="handleSuccess" :can-fullscreen="false" width="45%">
    </handleModal>
  </div>
</template>

<script lang="ts" setup>
import { useTable, BasicTable, TableAction } from '@/components/Table';
import { columns, checkFormSchemas } from './data';
import { useModal } from '/@/components/Modal';
import handleModal from './handleModal.vue';
import { list, saveOrUpdate, removeById } from '@/api/merchants/type';
import { useMessage } from '@monorepo-yysz/hooks';

const { createConfirm, createErrorModal, createSuccessModal } = useMessage();
const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  useSearchForm: true,
  bordered: true,
  showIndexColumn: false,
  api: list,
  formConfig: {
    labelWidth: 120,
    schemas: checkFormSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  actionColumn: {
    title: '操作',
    width: 340,
    dataIndex: 'action',
    fixed: undefined,
    align: 'left',
    auth: ['/merchants/type/add', '/merchants/manage/delete', '/merchants/type/edit']
  },
});

// 弹窗事件
const [registerModal, { openModal, closeModal }] = useModal();
function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

function handleView(record, disabled) {
  openModal(true, {
    record,
    isUpdate: true,
    disabled,
  });
}

function handleDelete({ typeName, autoId }: Recordable) {
  createConfirm({
    iconType: 'warning',
    content: `请确认要删除:${typeName}？`,
    onOk: function () {
      removeById(autoId).then(({ code, message }) => {
        if (code === 200) {
          createSuccessModal({ content: `删除成功` });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleSuccess({ isUpdate, values }) {
  saveOrUpdate(values).then(({ code, message }) => {
    if (code === 200) {
      createSuccessModal({
        content: `${isUpdate ? '修改' : '新增'}成功`,
      });
      reload();
      closeModal();
    } else {
      createErrorModal({
        content: `${isUpdate ? '修改' : '新增'}失败! ${message}`,
      });
    }
  });
}
</script>