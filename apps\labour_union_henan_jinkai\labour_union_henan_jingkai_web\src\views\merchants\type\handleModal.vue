<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '/@/components/Modal';
import { useForm, BasicForm } from '/@/components/Form';
import { modalFormItem } from './data';
import { maxSortNumber } from '@/api/merchants/type';

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const disabled = ref(false);

const isUpdate = ref(false);

const title = computed(() => {
  return unref(isUpdate) ? (unref(disabled) ? `内容详情` : `编辑分类`) : `新增分类`;
});

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const form = computed(() => {
  return modalFormItem(unref(disabled));
});

const [registerForm, { resetFields, validate, setFieldsValue, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data.disabled;
  isUpdate.value = !!data.isUpdate;
  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
    });
  } else {
    console.log(11321);

    await maxSortNumber({}).then(res => {
      const { data } = res;
      setFieldsValue({
        sortNumber: data,
      });
    });
  }

  setProps({ disabled: unref(disabled) });

  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});

async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    if (isUpdate.value) {
      values.autoId = record.value?.autoId;
    }
    // 判断详情图是不是数组
    if (Array.isArray(values.scenicImages)) {
      values.scenicImages = values.scenicImages.join(',');
    }
    emit('success', {
      values,
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
