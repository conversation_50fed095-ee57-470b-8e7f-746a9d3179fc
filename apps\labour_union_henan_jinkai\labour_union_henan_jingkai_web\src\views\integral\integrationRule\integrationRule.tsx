import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { YESNOEnum } from '@monorepo-yysz/enums';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '规则名称',
      dataIndex: 'ruleName',
      width: 160,
    },
    {
      title: '积分分值',
      dataIndex: 'integralScore',
      width: 120,
    },
    {
      title: '奖励类型',
      dataIndex: 'incType',
      width: 120,
      customRender({ text }) {
        const name = dictionary.getDictionaryMap.get(`IntegralIncType_${text}`)?.dictName;
        return <span title={name}>{name}</span>;
      },
    },
    {
      title: '有效次数',
      dataIndex: 'effectiveTimes',
      width: 120,
    },
    // {
    //   title: '周期天数',
    //   dataIndex: 'cycleDay',
    //   width: 120,
    // },
    // {
    //   title: '周期奖励',
    //   dataIndex: 'cycleReward',
    //   width: 120,
    // },
    {
      title: '启用状态',
      dataIndex: 'enableDisable',
      width: 100,
      customRender: ({ text }) => {
        return (
          <span class={text === YESNOEnum.YES ? 'text-green-500' : 'text-red-500'}>
            {dictionary.getDictionaryMap.get(`EnableOrDisable_${text}`)?.dictName}
          </span>
        );
      },
    },
    {
      title: '规则描述',
      dataIndex: 'ruleRemark',
    },
  ];
};

//搜索框设置
export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'ruleName',
      label: '规则名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
  ];
};

/*
 * 弹出框的设置
 * @param {Boolean} disabled 是否为system
 * @param {Boolean} isUpdate 是否为编辑
 * @param {Boolean} isLook   是否为查看
 *
 * */
export const modalForm = (isUpdate: boolean, ruleType, ruleCode): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'ruleName',
      label: '积分规则名称',
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      dynamicDisabled: isUpdate,
      componentProps: {
        autocomplete: 'off',
      },
    },
    {
      field: 'integralScore',
      label: '积分分值',
      required: true,
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'incType',
      label: '积分奖励类型',
      required: true,
      component: 'RadioGroup',
      defaultValue: 'DAY',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('IntegralIncType') as RadioGroupChildOption[],
      },
    },
    {
      field: 'effectiveTimes',
      label: '有效次数',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 0,
      },
    },
    // {
    //   field: 'cycleDay',
    //   label: '额外奖励周期-按天统计',
    //   component: 'InputNumber',
    //   rulesMessageJoinLabel: true,
    //   defaultValue: 0,
    //   componentProps: {
    //     min: 0,
    //   },
    // },
    // {
    //   field: 'cycleReward',
    //   label: '满足周期额外奖励积分',
    //   component: 'InputNumber',
    //   rulesMessageJoinLabel: true,
    //   defaultValue: 0,
    //   componentProps: {
    //     min: 0,
    //   },
    // },
    {
      field: 'enableDisable',
      label: '启用状态',
      component: 'RadioGroup',
      defaultValue: YESNOEnum.YES,
      required: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('EnableOrDisable') as RadioGroupChildOption[],
      },
    },
    {
      field: 'ruleRemark',
      label: '规则描述',
      component: 'InputTextArea',
      componentProps: {
        showCount: true,
        maxlength: 255,
      },
    },
  ];
};
