<template>
  <LoginFormTitle
    v-show="getShow"
    class="enter-x"
  />
  <Form
    class="enter-x"
    :model="formData"
    :rules="getFormRules"
    ref="formRef"
    v-show="getShow"
    @keypress.enter="handleLogin"
  >
    <FormItem
      name="account"
      class="enter-x mb-6"
    >
      <Input
        size="large"
        v-model:value="formData.account"
        :placeholder="t('sys.login.accountPlaceholder')"
        class="fix-auto-fill rounded-xl border-gray-200 hover:border-blue-400 focus:border-blue-500 transition-all duration-300"
      >
        <template #addonBefore>
          <div
            class="flex items-center justify-center w-12 h-full bg-gray-50 rounded-l-xl border-r border-gray-200"
          >
            <img
              :src="userName"
              class="w-[17px] h-[20px]"
            />
          </div>
        </template>
      </Input>
    </FormItem>
    <FormItem
      name="password"
      class="enter-x mb-6"
    >
      <InputPassword
        size="large"
        visibilityToggle
        v-model:value="formData.password"
        :placeholder="t('sys.login.passwordPlaceholder')"
        class="rounded-xl border-gray-200 hover:border-blue-400 focus:border-blue-500 transition-all duration-300"
      >
        <template #addonBefore>
          <div
            class="flex items-center justify-center w-12 h-full bg-gray-50 rounded-l-xl border-r border-gray-200"
          >
            <img
              :src="password"
              class="w-[17px] h-[20px]"
            />
          </div>
        </template>
      </InputPassword>
    </FormItem>

    <FormItem
      name="verifyCode"
      class="enter-x mb-6"
    >
      <Input
        size="large"
        v-model:value="formData.verifyCode"
        placeholder="请输入验证码"
        class="rounded-xl border-gray-200 hover:border-blue-400 focus:border-blue-500 transition-all duration-300"
      >
        <template #addonBefore>
          <div
            class="flex items-center justify-center w-12 h-full bg-gray-50 rounded-l-xl border-r border-gray-200"
          >
            <img
              :src="verify"
              class="w-[17px] h-[20px]"
            />
          </div>
        </template>
        <template #suffix>
          <div class="flex items-center h-full pr-2">
            <img
              :src="codeUrl"
              @click="handleChangeCode"
              class="cursor-pointer border border-gray-200 rounded-lg h-8 w-24 object-cover hover:border-blue-400 transition-all duration-300"
              title="点击刷新验证码"
            />
          </div>
        </template>
      </Input>
    </FormItem>

    <ARow class="enter-x mb-6">
      <ACol :span="12">
        <FormItem class="mb-0">
          <Checkbox
            v-model:checked="rememberMe"
            size="small"
            class="text-gray-600 hover:text-blue-600 transition-colors duration-300"
          >
            {{ t('sys.login.rememberMe') }}
          </Checkbox>
        </FormItem>
      </ACol>
    </ARow>

    <FormItem class="enter-x mb-0">
      <Button
        type="primary"
        size="large"
        block
        @click="handleLogin"
        :loading="loading"
        class="h-12 rounded-xl font-semibold text-base bg-gradient-to-r from-blue-500 to-purple-600 border-none hover:from-blue-600 hover:to-purple-700 transform hover:scale-[1.02] transition-all duration-300 shadow-lg hover:shadow-xl"
      >
        {{ t('sys.login.loginButton') }}
      </Button>
    </FormItem>
  </Form>
</template>
<script lang="ts" setup>
import { reactive, ref, unref, computed, onMounted } from 'vue';

import { Checkbox, Form, Input, Row, Col, Button } from 'ant-design-vue';

import LoginFormTitle from './LoginFormTitle.vue';

import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@monorepo-yysz/hooks';
import { useUserStore } from '@/store/modules/user';
import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
import { useDesign } from '@monorepo-yysz/hooks';
import { generatorWebVerifyCode } from '@/api';
import userName from '@/assets/images/login/userName.png';
import password from '@/assets/images/login/password.png';
import verify from '@/assets/images/login/verify.png';

const ACol = Col;
const ARow = Row;
const FormItem = Form.Item;
const InputPassword = Input.Password;

const { t } = useI18n();
const { notification, createErrorModal } = useMessage();
const { prefixCls } = useDesign('login');
const userStore = useUserStore();

const codeUrl = ref();

const verifyCodeId = ref();

const { getLoginState } = useLoginState();
const { getFormRules } = useFormRules();

const formRef = ref();
const loading = ref(false);
const rememberMe = ref(false);

const formData = reactive({
  account: undefined,
  password: undefined,
  verifyCode: undefined,
});

const { validForm } = useFormValid(formRef);

const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

async function handleChangeCode() {
  await getCode();
}

async function getCode() {
  const { imageBase64Data, verifyCodeId: id } = await generatorWebVerifyCode();
  codeUrl.value = imageBase64Data;
  verifyCodeId.value = id;
}

async function handleLogin() {
  const data = await validForm();
  if (!data) return;
  try {
    loading.value = true;
    const userInfo = await userStore.login({
      pwd: data.password,
      account: data.account,
      device: 'WEBSITE',
      authSystem: 'BACKEND',
      loginType: 'ACCOUNT',
      verifyCodeId: unref(verifyCodeId),
      verifyCode: data.verifyCode,
      mode: 'none', //不要默认的错误提示
    });
    if (userInfo) {
      notification.success({
        message: t('sys.login.loginSuccessTitle'),
        description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.nickname || userInfo.account}`,
        duration: 3,
      });
    }
  } catch (error) {
    createErrorModal({
      title: t('sys.api.errorTip'),
      content: (error as unknown as Error).message || t('sys.api.networkExceptionMsg'),
      getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
    });
    getCode();
  } finally {
    loading.value = false;
    // getCode();
    formData.verifyCode = undefined;
  }
}

onMounted(() => {
  getCode();
});
</script>
