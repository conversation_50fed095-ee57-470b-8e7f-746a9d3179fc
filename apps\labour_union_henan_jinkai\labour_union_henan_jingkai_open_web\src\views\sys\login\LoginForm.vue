<template>
  <LoginFormTitle
    v-show="getShow"
    class="enter-x"
  />
  <Form
    class="p-4 enter-x"
    :model="formData"
    :rules="getFormRules"
    ref="formRef"
    v-show="getShow"
    @keypress.enter="handleLogin"
  >
    <FormItem
      name="account"
      class="enter-x"
    >
      <Input
        size="large"
        v-model:value="formData.account"
        :placeholder="t('sys.login.accountPlaceholder')"
        class="fix-auto-fill"
      >
        <template #addonBefore>
          <img
            :src="userName"
            class="w-[17px] h-[20px]"
          />
        </template>
      </Input>
    </FormItem>
    <FormItem
      name="password"
      class="enter-x"
    >
      <InputPassword
        size="large"
        visibilityToggle
        v-model:value="formData.password"
        :placeholder="t('sys.login.passwordPlaceholder')"
      >
        <template #addonBefore>
          <img
            :src="password"
            class="w-[17px] h-[20px]"
          />
        </template>
      </InputPassword>
    </FormItem>

    <FormItem
      name="verifyCode"
      class="enter-x password-input rounded-[30px]"
    >
      <Input
        size="large"
        v-model:value="formData.verifyCode"
        placeholder="请输入验证码"
      >
        <template #addonBefore>
          <img
            :src="verify"
            class="w-[17px] h-[20px]"
          />
        </template>
        <template #suffix>
          <img
            :src="codeUrl"
            @click="handleChangeCode"
            :style="{
              position: 'absolute',
              right: 0,
              cursor: 'pointer',
            }"
            class="!border border-gray-200 border-solid z-10 !h-full verify-code w-[135px] rounded-r-[30px]"
          />
        </template>
      </Input>
    </FormItem>

    <ARow class="enter-x">
      <ACol :span="12">
        <FormItem>
          <!-- No logic, you need to deal with it yourself -->
          <Checkbox
            v-model:checked="rememberMe"
            size="small"
          >
            {{ t('sys.login.rememberMe') }}
          </Checkbox>
        </FormItem>
      </ACol>
    </ARow>

    <FormItem class="enter-x">
      <Button
        type="primary"
        size="large"
        block
        @click="handleLogin"
        :loading="loading"
      >
        {{ t('sys.login.loginButton') }}
      </Button>
      <!-- <Button
        size="large"
        class="mt-4 enter-x"
        block
        @click="setLoginState(LoginStateEnum.REGISTER)"
      >
        {{ t('sys.login.registerButton') }}
      </Button> -->
    </FormItem>
  </Form>
</template>
<script lang="ts" setup>
import { reactive, ref, unref, computed, onMounted } from 'vue';

import { Checkbox, Form, Input, Row, Col, Button } from 'ant-design-vue';

import LoginFormTitle from './LoginFormTitle.vue';

import { useI18n } from '@/hooks/web/useI18n';
import { useMessage } from '@monorepo-yysz/hooks';
import { useUserStore } from '@/store/modules/user';
import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
import { useDesign } from '@monorepo-yysz/hooks';
import { generatorWebVerifyCode } from '@/api';
import userName from '@/assets/images/login/userName.png';
import password from '@/assets/images/login/password.png';
import verify from '@/assets/images/login/verify.png';

const ACol = Col;
const ARow = Row;
const FormItem = Form.Item;
const InputPassword = Input.Password;

const { t } = useI18n();
const { notification, createErrorModal } = useMessage();
const { prefixCls } = useDesign('login');
const userStore = useUserStore();

const codeUrl = ref();

const verifyCodeId = ref();

const { getLoginState } = useLoginState();
const { getFormRules } = useFormRules();

const formRef = ref();
const loading = ref(false);
const rememberMe = ref(false);

const formData = reactive({
  account: undefined,
  password: undefined,
  verifyCode: undefined,
});

const { validForm } = useFormValid(formRef);

const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

async function handleChangeCode() {
  await getCode();
}

async function getCode() {
  const { imageBase64Data, verifyCodeId: id } = await generatorWebVerifyCode();
  codeUrl.value = imageBase64Data;
  verifyCodeId.value = id;
}

async function handleLogin() {
  const data = await validForm();
  if (!data) return;
  try {
    loading.value = true;
    const userInfo = await userStore.login({
      pwd: data.password,
      account: data.account,
      device: 'WEBSITE',
      authSystem: 'BACKEND',
      loginType: 'ACCOUNT',
      verifyCodeId: unref(verifyCodeId),
      verifyCode: data.verifyCode,
      mode: 'none', //不要默认的错误提示
    });
    if (userInfo) {
      notification.success({
        message: t('sys.login.loginSuccessTitle'),
        description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.nickname || userInfo.account}`,
        duration: 3,
      });
    }
  } catch (error) {
    createErrorModal({
      title: t('sys.api.errorTip'),
      content: (error as unknown as Error).message || t('sys.api.networkExceptionMsg'),
      getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
    });
    getCode();
  } finally {
    loading.value = false;
    // getCode();
    formData.verifyCode = undefined;
  }
}

onMounted(() => {
  getCode();
});
</script>
