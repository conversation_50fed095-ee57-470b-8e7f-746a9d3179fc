import { BasicColumn, FormSchema } from '@/components/Table';
import { useDictionary } from '@/store/modules/dictionary';
import { Image, Input } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/user';
import { Tinymce } from '@/components/Tinymce';
import { CheckboxOptionType } from 'ant-design-vue/lib';
import { userInfoSearch } from '@/api/productManagement/integralExchangeRecord';
import { getSpecifications } from '@/api/productManagement';
import { startsWith } from 'lodash-es';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
//列表配置
export const columns = (phProductFlg): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
    },
    {
      title: '一级商户',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '商品封面',
      dataIndex: 'productCoverImg',
      width: 90,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '商品类型',
      dataIndex: 'productType',
      width: 90,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`productType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '商品栏目',
      dataIndex: 'integralProductColumn',
      width: 90,
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`integralProductColumn_${text}`)?.dictName}</span>
        );
      },
    },
    {
      title: '商品来源',
      dataIndex: 'sourceType',
      width: 90,
      ifShow: phProductFlg !== 'y',
      customRender: ({ text }) => {
        return (
          <span>{dictionary.getDictionaryMap.get(`productSourcetype_${text}`)?.dictName}</span>
        );
      },
    },
    {
      title: '上架状态',
      dataIndex: 'state',
      width: 90,
      customRender: ({ text, record }) => {
        const { state } = record;
        return (
          <span class={state == 'up' ? 'text-green-500' : state == 'down' ? 'text-red-500' : ''}>
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
    {
      title: '所属工会',
      dataIndex: 'provideUnionName',
      width: 200,
    },
  ];
};

//顶部菜单栏搜索条件配置
export const formSchemas = (flg): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productName',
      label: '商品名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'integralProductColumn',
      label: '商品栏目',
      component: 'Select',
      colProps: { span: 6 },
      ifShow: flg !== true,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('integralProductColumn'),
        };
      },
    },
    {
      field: 'state',
      label: '上架状态',
      component: 'Select',
      colProps: { span: 6 },
      ifShow: flg !== true,
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleEnable'),
        };
      },
    },
    {
      field: 'tenantChildFlag',
      component: 'Select',
      label: '数据范围',
      colProps: { span: 4 },
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '本级', value: 0 },
          { label: '下一级', value: 1 },
          { label: '所有下级', value: 2 },
        ],
      }
    },
  ];
};

//详情弹框配置
export const modalForm = (isInclusive, isUpdate: boolean): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productName',
      label: '商品名称',
      colProps: { span: 24 },
      labelWidth: 80,
      required: true,
      rulesMessageJoinLabel: true,
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 100,
      },
    },
    {
      field: 'productType',
      label: '商品类型',
      colProps: { span: 12 },
      labelWidth: 80,
      required: true,
      component: 'RadioGroup',
      // dynamicDisabled({ values }) {
      //   return values.sourceType === 'inclusive' ? true : false
      // },
      slot: 'productType',
      // componentProps: function () {
      //   return {
      //     options: dictionary.getDictionaryOpt.get('productType') as CheckboxOptionType[],
      //     placeholder: '请选择商品类型',
      //   };
      // },
    },
    {
      field: 'integralPayment',
      label: '领取方式',
      colProps: { span: 12 },
      labelWidth: 80,
      required: true,
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      dynamicDisabled({ values }) {
        return values.consumeType === 'mix' ? true : false;
      },
      // slot: 'integralPayment',
      componentProps({ formModel }) {
        if ('virtual' === formModel.productType) {
          return { options: [{ label: '线下核销', value: '2' }] as RadioGroupChildOption[] }
        } else {
          return { options: dictionary.getDictionaryOpt.get('integralPayment') as RadioGroupChildOption[] }
        }
      },
    },
    {
      labelWidth: 80,
      field: 'sourceType',
      label: '商品来源',
      colProps: { span: 12 },
      required: true,
      ifShow: isInclusive !== true,
      component: 'CheckboxGroup',
      defaultValue: 'provide',
      slot: 'sourceType',
      /*      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('productSourcetype'),
          placeholder: '请选择商品来源',
        };
      },*/
    },
    //普惠商户 选择商品
    {
      labelWidth: 80,
      field: 'companyName',
      label: '一级商户',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      slot: 'productButton',
      ifShow({ values }) {
        return values.sourceType === 'inclusive' ? true : false;
      },
    },
    //自主提供选择商家
    {
      labelWidth: 80,
      field: 'companyName',
      label: '一级商户',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      slot: 'button',
      dynamicDisabled: isUpdate,
      ifShow({ values }) {
        return values.sourceType === 'inclusive' ? false : true && isUpdate === false;
      },
    },
    {
      labelWidth: 80,
      field: 'companyId',
      label: '商户id',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: isUpdate,
      show: false
    },
    {
      labelWidth: 80,
      field: 'companyName',
      label: '一级商户',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      dynamicDisabled: isUpdate,
      ifShow({ values }) {
        return values.sourceType === 'inclusive' ? false : true && isUpdate === true;
      },
    },
    {
      labelWidth: 80,
      field: 'parentAutoId',
      label: '一级商家autoid',
      required: true,
      colProps: { span: 12 },
      component: 'InputNumber',
      dynamicDisabled: isUpdate,
      show: false
    },
    {
      labelWidth: 80,
      field: 'childProductCompanyName',
      label: '二级商户',
      required: false,
      colProps: { span: 24 },
      component: 'ApiSelect',
      slot: 'childButton',
      dynamicDisabled: isUpdate,
      ifShow({ values }) {
        return values.companyName !== undefined;
      },
    },
    {
      labelWidth: 80,
      field: 'childProductCompany',
      label: '二级商户ID',
      required: false,
      colProps: { span: 24 },
      component: 'ApiSelect',
      dynamicDisabled: isUpdate,
      show: false,
    },
    {
      labelWidth: 80,
      field: 'consumeType',
      label: '消耗类型',
      colProps: { span: 12 },
      required: true,
      ifShow: isInclusive !== true,
      component: 'CheckboxGroup',
      defaultValue: 'integral',
      slot: 'consumeType',
      /*      componentProps:{
          options: filter(
            cloneDeep (dictionary.getDictionaryOpt.get('consumeType')),
            v => v.value !== 'CNY',
          ),
          placeholder: '请选择消耗类型',
      },*/
    },
    {
      labelWidth: 120,
      field: 'maxReceiveNum',
      label: '最多领取次数',
      colProps: { span: 12 },
      ifShow: isInclusive !== true,
      rulesMessageJoinLabel: true,
      component: 'InputNumber',
      componentProps: {
        min: 1
      }
    },
    {
      labelWidth: 85,
      field: 'productCoverImg',
      label: '商品封面图',
      colProps: { span: 12 },
      required: true,
      component: 'CropperForm',
      slot: 'picOnlyOne',
      dynamicDisabled({ values }) {
        return values.sourceType === 'inclusive' ? true : false;
      },
    },
    {
      labelWidth: 80,
      field: 'integralProductColumn',
      label: '商品栏目',
      colProps: { span: 24 },
      required: true,
      component: 'RadioGroup',
      rulesMessageJoinLabel: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('integralProductColumn') as CheckboxOptionType[],
      },
    },
    {
      field: 'productPublicityImg',
      label: '宣传图',
      labelWidth: 60,
      colProps: { span: 24 },
      required: true,
      component: 'CropperForm',
      slot: 'pic',
      rest: true,
    },
    {
      field: 'contractPhone',
      label: '商家电话',
      colProps: { span: 12 },
      required: true,
      labelWidth: 80,
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      field: 'exchangeNotice',
      label: '兑换须知',
      labelWidth: 80,
      required: true,
      component: 'InputTextArea',
      colProps: { span: 24 },
      defaultValue: '兑后48小时内发货，确认兑换过后积分不退港澳台、海外、受其他外在影响或突发情况地区暂不支持发货。',
      componentProps: {
        autocomplete: 'off',
        showCount: true,
        maxlength: 500,
        autoSize: { minRows: 1, maxRows: 6 },
      },
    },
    {
      field: 'productIntroduce',
      label: '商品简介',
      colProps: { span: 24 },
      labelWidth: 80,
      required: true,
      component: 'Input',
      rulesMessageJoinLabel: true,
      render({ model, field, disabled }) {
        return (
          <Tinymce
            value={model[field]}
            options={{ readonly: !!disabled }}
            onChange={value => {
              model[field] = value;
            }}
          ></Tinymce>
        );
      },
    },
    {
      field: 'address',
      required: true,
      label: '商户地址',
      component: 'MapSelect',
      labelWidth: 80,
      rest: true,
      ifShow: isInclusive !== true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps({ formModel }) {
        return {
          onChangeLnglat: lnglat => (formModel['addressCoordinate'] = lnglat),
          lnglat: formModel['addressCoordinate'],
        };
      },
    },
    {
      field: 'addressCoordinate',
      label: '商户坐标',
      colProps: { span: 24 },
      component: 'ShowSpan',
      rulesMessageJoinLabel: true,
      show: false,
    },

    // {
    //   field: 'address',
    //   label: '商户地址',
    //   colProps: { span: 24 },
    //   labelWidth: 80,
    //   required: true,
    //   ifShow: isInclusive !== true,
    //   component: 'Input',
    //   dynamicDisabled: true,
    //   rest: true,
    //   rulesMessageJoinLabel: true,
    //   render({ model, field, disabled }) {
    //     const lonlat = model['addressCoordinate'];
    //     let lnglat: Nullable<Recordable> = null;

    //     if (!!lonlat) {
    //       lnglat = { lng: lonlat.split(',')[0], lat: lonlat.split(',')[1] };
    //     }

    //     return (
    //       <div class={`h-50vh w-full`}>
    //         {/* <GlobBMap
    //           search={true}
    //           disabled={!disabled}
    //           boundaryName={model[field]}
    //           placeholder={`请输入地址`}
    //           customVal={lnglat}
    //           onChange={({ name, lonlat }) => {
    //             model[field] = name
    //             model['addressCoordinate'] = lonlat
    //           }}
    //         /> */}
    //       </div>
    //     );
    //   },
    // },
    // {
    //   field: 'addressCoordinate',
    //   label: '商户坐标',
    //   colProps: { span: 24 },
    //   component: 'ShowSpan',
    //   rulesMessageJoinLabel: true,
    //   show: false,
    // },
  ];
};

//顶部菜单栏搜索条件配置
export const querySpecifications = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'productSubName',
      label: '商品规格名称',
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入商品规格名称',
        autocomplete: 'off',
      },
    },
    {
      field: 'saleState',
      label: '上架状态',
      component: 'Select',
      colProps: { span: 8 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('saleEnable'),
          placeholder: '请选择上架状态',
        };
      },
    },
  ];
};

//添加时候规格列表
export const specificationsColumns = (isUpdate: boolean): BasicColumn[] => {

  return [
    {
      title: '规格名',
      dataIndex: 'productSubName',
      width: 300,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 180,
    },
    {
      title: '销售量',
      dataIndex: 'saleNum',
      width: 120,
      ifShow: isUpdate,
    },
    {
      title: '库存类型',
      dataIndex: 'reserveType',
      width: 150,
    },
    {
      title: '库存量(件)',
      dataIndex: 'reserve',
      width: 120,
    },
    {
      title: '消耗积分',
      dataIndex: 'nowIntegral',
      width: 120,
    },
    {
      title: '商品价格(元)',
      dataIndex: 'nowPrice',
      width: 120,
    },
  ];
};

//查询积分时候规格列表
export const querySpecificationsColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '规格名',
      dataIndex: 'productSubName',
      width: 150,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '现积分',
      dataIndex: 'nowIntegral',
      width: 100,
    },
    {
      title: '现价(元)',
      dataIndex: 'nowPrice',
      width: 100,
    },
    {
      title: '销售量',
      dataIndex: 'saleNum',
      width: 100,
    },
    {
      title: '库存类型',
      dataIndex: 'reserveType',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`reserveType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '库存量',
      dataIndex: 'reserve',
      width: 100,
      customRender: ({ text, record }) => {
        const { reserveType } = record;
        return <span>{reserveType === 'limited' ? text : '--'}</span>;
      },
    },
    {
      title: '上架状态',
      dataIndex: 'saleState',
      width: 100,
      customRender: ({ text, record }) => {
        const { saleState } = record;
        return (
          <span
            class={saleState == 'up' ? 'text-green-500' : saleState == 'down' ? 'text-red-500' : ''}
          >
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
  ];
};

//查询普惠商品时候规格列表
export const querypuhuiColumns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '规格名',
      dataIndex: 'productSubName',
      width: 150,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '原价(元)',
      dataIndex: 'oldPrice',
      width: 100,
    },
    {
      title: '现价(元)',
      dataIndex: 'nowPrice',
      width: 100,
    },
    {
      title: '销售量',
      dataIndex: 'saleNum',
      width: 100,
    },
    {
      title: '库存类型',
      dataIndex: 'reserveType',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`reserveType_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '库存量',
      dataIndex: 'reserve',
      width: 100,
      customRender: ({ text, record }) => {
        const { reserveType } = record;
        return <span>{reserveType === 'limited' ? text : '--'}</span>;
      },
    },
    {
      title: '上架状态',
      dataIndex: 'saleState',
      width: 100,
      customRender: ({ text, record }) => {
        const { saleState } = record;
        return (
          <span
            class={saleState == 'up' ? 'text-green-500' : saleState == 'down' ? 'text-red-500' : ''}
          >
            {dictionary.getDictionaryMap.get(`saleEnable_${text}`)?.dictName}
          </span>
        );
      },
    },
  ];
};

//顶部筛选商户条件参数
export const queryCompanyParams = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'companyName',
      label: '商户名称',
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入商户名称',
        autocomplete: 'off',
      },
    },
    {
      field: 'areaCode',
      label: '所属区域',
      component: 'Select',
      colProps: { span: 8 },
      componentProps: function () {
        return {
          options: dictionary.getDictionaryOpt.get('quxian'),
          placeholder: '请选择所属区域',
        };
      },
    },
  ];
};

//查询商户列表
export const queryCompanyList = (): BasicColumn[] => {
  const dictionary = useDictionary();
  const userStore = useUserStore();
  return [
    {
      title: '商户id',
      dataIndex: 'companyId',
      defaultHidden: true,
    },
    {
      title: '商户名称',
      dataIndex: 'companyName',
      width: 100,
    },
    {
      title: '商家封面',
      dataIndex: 'companyIcon',
      width: 100,
      customRender: ({ text }) => {
        return (
          <Image
            src={startsWith(text, 'http') ? text : userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    {
      title: '联系方式',
      dataIndex: 'contractPhone',
      width: 100,
    },
    {
      title: '所属区域',
      dataIndex: 'areaCode',
      width: 100,
      customRender: ({ text }) => {
        return <span>{dictionary.getDictionaryMap.get(`regionCode_${text}`)?.dictName}</span>;
      },
    },
    {
      title: '商户状态',
      dataIndex: 'state',
      width: 100,
      customRender: ({ text, record }) => {
        const { state } = record;
        return (
          <span
            class={state == 'normal' ? 'text-green-500' : state == 'down' ? 'text-red-500' : ''}
          >
            {dictionary.getDictionaryMap.get(`commonStatus_${text}`)?.dictName}
          </span>
        );
      },
    },
  ];
};

export const modalColumns = (consumeType: string): BasicColumn[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      title: '兑换人',
      dataIndex: 'userName',
    },
    {
      title: '规格名',
      customRender({ record }) {
        const productSubName = consumeType === 'integral' ? record?.productSubName : record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productSubName;
        return <span title={productSubName}>{productSubName}</span>;
      },
    },
    {
      title: '兑换时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '规格封面',
      dataIndex: 'productSubImg',
      width: 100,
      customRender: ({ record }) => {
        const text = consumeType === 'integral' ? record?.productSubImg : record?.snapshotVo?.transProductSnapshot?.productInfoList[0]?.priceListInfo[0]?.productSubImg;
        return (
          <Image
            src={userStore.getPrefix + text}
            width={50}
            height={50}
          ></Image>
        );
      },
    },
    // {
    //   title: '提货方式',
    //   dataIndex: 'integralPayment',
    //   customRender({ text }) {
    //     const name = dictionary.getDictionaryMap.get(`integralPayment_${text}`)?.dictName || '';
    //     return <span title={name}>{name}</span>;
    //   },
    // },
    {
      title: '状态',
      dataIndex: '',
      customRender({ record }) {
        let name = '';
        if (consumeType === 'integral') {
          if ('1' === record?.integralPayment) {
            name =
              dictionary.getDictionaryMap.get(`transOrderState_${record?.deliveryStatus}`)
                ?.dictName || '';
          } else {
            name = dictionary.getDictionaryMap.get(`qrCodeState_${record?.state}`)?.dictName || '';
          }

        } else {
          name =
            dictionary.getDictionaryMap.get(`transOrderState_${record?.orderState}`)?.dictName || '';
        }

        return <span title={name}>{name}</span>;
      },
      width: 150,
    },
  ];
};

export const columnSchemas = (productId, integralPayment,consumeType: string): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();

  return [
    {
      field: 'userId',
      label: '兑换人',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          api: userInfoSearch,
          resultField: 'data',
          params: {consumeType},
          alwaysLoad: true,
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.userName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'userName', value: 'userId' },
        };
      },
    },
    {
      field: 'productSubId',
      label: '规格名',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({ formActionType }) => {
        return {
          api: getSpecifications,
          resultField: 'data',
          params: { productId: productId ? productId : '', systemQueryType: 'manage' },
          alwaysLoad: true,
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.productSubName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'productSubName', value: 'productSubId' },
        };
      },
    },

    // {
    //   field: '1' === integralPayment ? 'deliveryStatus' : 'state',
    //   label: '状态',
    //   colProps: { span: 6 },
    //   component: 'Select',
    //   rulesMessageJoinLabel: true,
    //   componentProps: function () {
    //     return {
    //       options:
    //         '1' === integralPayment
    //           ? [
    //               { label: '待发货', value: 'deliver' },
    //               { label: '待收货', value: 'receive' },
    //               { label: '交易成功', value: 'over' },
    //             ]
    //           : dictionary.getDictionaryOpt.get('qrCodeState'),
    //     };
    //   },
    // },
  ];
};

export const modalFormItem = (integralPayment): FormSchema[] => {
  const dictionary = useDictionary();

  const userStore = useUserStore();
  return [
    {
      field: '',
      label: '兑换信息',
      component: 'Divider',
    },
    {
      field: 'userName',
      label: '兑换人',
      colProps: { span: 12 },

      component: 'Input',
      rulesMessageJoinLabel: true,
    },
    {
      field: 'createTime',
      label: '兑换时间',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'productName',
      label: '商品名',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'productSubName',
      label: '规格名',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
    },
    {
      field: 'productCoverImg',
      label: '商品封面图',
      colProps: { span: 12 },

      component: 'CropperForm',
    },
    {
      field: 'productSubImg',
      label: '规格封面',
      colProps: { span: 12 },

      component: 'CropperForm',
    },
    {
      field: '1' === integralPayment ? 'deliveryStatus' : 'state',
      label: '状态',
      colProps: { span: 12 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      componentProps: function () {
        return {
          options:
            '1' === integralPayment
              ? [
                { label: '待发货', value: 'deliver' },
                { label: '待收货', value: 'receive' },
                { label: '交易成功', value: 'over' },
              ]
              : dictionary.getDictionaryOpt.get('qrCodeState'),
        };
      },
    },
    {
      field: '',
      label: '收货信息',
      component: 'Divider',
      ifShow: '1' === integralPayment,
    },
    {
      field: 'receiverName',
      label: '收货人姓名',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      ifShow: '1' === integralPayment,
    },
    {
      field: 'receiverPhone',
      label: '收货人电话',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
      ifShow: '1' === integralPayment,
    },
    {
      field: 'detailArea',
      label: '所在地区',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
      ifShow: '1' === integralPayment,
    },
    {
      field: 'detailAddress',
      label: '详细地址',
      colProps: { span: 12 },
      component: 'Input',

      rulesMessageJoinLabel: true,
      ifShow: '1' === integralPayment,
    },
    {
      field: '',
      label: '发货信息',
      component: 'Divider',
      ifShow: '1' === integralPayment,
    },
    {
      field: 'writeOffUserName',
      label: '发货人姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 20,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: 'writeOffUserPhone',
      label: '发货人手机号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 18,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: 'logisticsCompany',
      label: '物流公司',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: 'logisticsNumber',
      label: '物流单号',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 50,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: 'shippingAddress',
      label: '发货地址',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 300,
        showCount: true,
      },
      ifShow: '1' === integralPayment,
    },
    {
      field: '',
      label: '核销信息',
      component: 'Divider',
      ifShow: '2' === integralPayment,
    },
    {
      field: 'writeOffUserName',
      label: '核销人姓名',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 20,
        showCount: true,
      },
      ifShow: '2' === integralPayment,
    },
    {
      field: 'writeOffUserPhone',
      label: '核销人手机号码',
      colProps: { span: 12 },
      component: 'Input',
      required: true,
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
        maxlength: 18,
        showCount: true,
      },
      ifShow: '2' === integralPayment,
    },
  ];
};
