<template>
  <div>
    <BasicTable
      @register="registerTable"
      :clickToRowSelect="false"
    >
      <template #toolbar>
        <a-button
          type="primary"
          @click="handleClick"
          auth="/messageSending/add"
        >
          信息发送</a-button
        >
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'carbon:task-view',
                label: '详情',
                type: 'default',
                onClick: handleInfoView.bind(null, record),
                auth: '/messageSending/view',
              },
              {
                icon: 'carbon:task-view',
                label: '信息发送详情',
                type: 'default',
                onClick: handleView.bind(null, record),
                auth: '/messageSending/view',
              },
              {
                icon: 'fa6-solid:pen-to-square',
                label: '编辑',
                type: 'primary',
                disabled: isDisabled(record),
                onClick: handleEdit.bind(null, record),
                auth: '/messageSending/modify',
              },
              {
                icon: 'fluent:delete-16-filled',
                label: '删除',
                type: 'primary',
                danger: true,
                disabled: isDisabled(record),
                onClick: handleDelete.bind(null, record),
                auth: '/messageSending/delete',
              },
              {
                icon: 'ant-design:audit-outlined',
                label: '审核',
                type: 'primary',
                onClick: handleApp.bind(null, record),
                disabled: record.mesStatus !== 'STATUS02',
                auth: '/messageSending/audit',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <MessageModal
      @register="registerModal"
      @success="handleSuccess"
      :canFullscreen="false"
      width="70%"
    />
    <AppSmsModal
      @register="registerAppSms"
      @success="handleAppSuccess"
      :canFullscreen="false"
      width="50%"
    />
    <DetailModal
      @register="detailModal"
      :canFullscreen="false"
      width="75%"
    />
  </div>
</template>

<script lang="ts" setup>
import { BasicTable, useTable, TableAction } from '@/components/Table';
import MessageModal from '@/views/commonMessage/messageSending/messageModal.vue';
import { useModal } from '@/components/Modal';
import {
  list,
  messageSend,
  deleteMessageTemplate,
  appSms,
  update,
  getDetails,
} from '@/api/messageSend';
import { columns, formSchemas } from '@/views/commonMessage/messageSending/messageSending';
import AppSmsModal from '@/views/commonMessage/messageSending/appSmsModal.vue';
import DetailModal from '@/views/commonMessage/messageSending/detailModal.vue';
import { useMessage } from '@monorepo-yysz/hooks';

const { createErrorModal, createSuccessModal, createConfirm } = useMessage();

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: columns(),
  showIndexColumn: false,
  api: list,
  authInfo: ['/messageSending/add'],
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas(),
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  searchInfo: {
    orderBy: 'updateTime',
    sortType: 'desc',
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 250,
    dataIndex: 'action',

    fixed: undefined,
    auth: [
      '/messageSending/modify',
      '/messageSending/audit',
      '/messageSending/view',
      '/messageSending/delete',
    ],
  },
});

const [registerModal, { openModal, closeModal: closeBasic }] = useModal();

const [registerAppSms, { openModal: openModalAppSms, closeModal }] = useModal();

const [detailModal, { openModal: openDetailModal }] = useModal();

const isDisabled = record => !['STATUS01', 'STATUS03'].includes(record.mesStatus);

function handleClick() {
  openModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

function handleApp(record) {
  openModalAppSms(true, {
    record,
  });
}

function handleDelete(record) {
  createConfirm({
    iconType: 'warning',
    content: `请确定删除${record.mesTitle}?`,
    onOk: function () {
      deleteMessageTemplate(record.autoId).then(res => {
        const { code, message } = res;

        if (code === 200) {
          createSuccessModal({ content: '删除成功!'  });
          reload();
        } else {
          createErrorModal({ content: `删除失败，${message}` });
        }
      });
    },
  });
}

function handleEdit(record) {
  getDetails(record).then(({ code, data }) => {
    if (code === 200) {
      openModal(true, {
        record: data,
        isUpdate: true,
        disabled: false,
      });
    }
  });
}

function handleInfoView(record) {
  getDetails(record).then(({ code, data }) => {
    if (code === 200) {
      openModal(true, {
        record: data,
        isUpdate: true,
        disabled: true,
      });
    }
  });
}

function handleView(record) {
  openDetailModal(true, {
    record,
    isUpdate: false,
  });
}

function handleSuccess({ values, isUpdate }) {
  if (isUpdate) {
    update(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: '编辑成功!'  });
        reload();
        closeBasic();
      } else {
        createErrorModal({ content: `编辑失败，${message}` });
      }
    });
  } else {
    messageSend(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: '新增成功' });
        reload();
        closeBasic();
      } else {
        createErrorModal({ content: `新增失败，${message}` });
      }
    });
  }
}

function handleAppSuccess({ values }) {
  appSms(values).then(res => {
    const { code, message } = res;
    if (code === 200) {
      createSuccessModal({ content: message || '操作成功!' });
      reload();
      closeModal();
    } else {
      createErrorModal({ content: `操作失败!${message}` });
    }
  });
}
</script>
