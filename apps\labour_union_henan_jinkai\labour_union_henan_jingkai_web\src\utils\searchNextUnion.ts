import { cloneDeep, filter } from 'lodash-es';
import { useDictionary } from '../store/modules/dictionary';
import { useUserStore } from '../store/modules/user';
import { FormSchema } from '../components/Form';

const dictionary = useDictionary();
const userStore = useUserStore();
export function searchNextUnionForm(): FormSchema[] {
  return [
    {
      field: 'queryCompanyId',
      label: '下级工会',
      colProps: { span: 5 },
      component: 'Select',
      rulesMessageJoinLabel: true,
      ifShow: userStore.getUserInfo.companyId === '6650f8e054af46e7a415be50597a99d5',
      componentProps: function () {
        return {
          options: filter(
            cloneDeep(dictionary.getDictionaryOpt.get(`unionsInfo`)),
            v => v.value !== '6650f8e054af46e7a415be50597a99d5'
          ),
        };
      },
    },
    {
      field: 'tenantChildFlag',
      component: 'Select',
      label: '数据范围',
      colProps: { span: 4 },
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '本级', value: 0 },
          { label: '下一级', value: 1 },
          { label: '所有下级', value: 2 },
        ],
      }
    },
  ];
}
