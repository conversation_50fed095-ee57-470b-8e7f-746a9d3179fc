import { Tooltip } from 'ant-design-vue';
import { map } from 'lodash-es';
import { BasicColumn, FormSchema } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { getList } from '/@/api/category';
import { uploadApi } from '/@/api/sys/upload';
import { maxSortNumber, getCategoryNamePinyin } from '/@/api/category';
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';
import { CheckboxOptionType } from 'ant-design-vue/lib';
import { list } from '@/api/category/newsReleaseUnion';
import { getReferToCategory } from '/@/api/category';

export const columns = (): BasicColumn[] => {
  const dictionary = useDictionary();
  return [
    {
      title: '主键',
      dataIndex: 'categoryId',
      defaultHidden: true,
    },
    {
      title: '序号',
      dataIndex: 'sort',
      width: 100,
    },
    {
      title: '栏目名称',
      dataIndex: 'categoryName',
    },
    {
      title: '栏目编码',
      dataIndex: 'categoryCode',
    },
    {
      title: '所属工会',
      dataIndex: 'companyName',
      width: 160,
    },
    // {
    //   title: '平台类型',
    //   dataIndex: 'platformType',
    //   width: 160,
    //   customRender: ({ text }) => {
    //     const textArr = text?.split(',');
    //     const all = map(
    //       textArr,
    //       v => dictionary.getDictionaryMap.get(`appType_${v}`)?.dictName
    //     )?.join(',');
    //     return (
    //       <Tooltip title={all}>
    //         <span>{all}</span>
    //       </Tooltip>
    //     );
    //   },
    // },
    // {
    //   title: '栏目类型',
    //   dataIndex: 'categoryType',
    //   width: 100,
    //   customRender: ({ text: v }) => {
    //     const text = dictionary.getDictionaryMap.get(`categoryType_${v}`)?.dictName;
    //     return (
    //       <Tooltip title={text}>
    //         <span>{text}</span>
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'categoryName',
      label: '栏目名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: {
        autocomplete: 'off',
      },
    },
    // {
    //   field: 'platformType',
    //   label: '平台类型',
    //   component: 'Select',
    //   colProps: { span: 6 },
    //   rulesMessageJoinLabel: true,
    //   componentProps: function () {
    //     return {
    //       options: dictionary.getDictionaryOpt.get('appType'),
    //     };
    //   },
    // },
    {
      field: 'queryCompanyId',
      label: '所属工会',
      component: 'ApiSelect',
      colProps: { span: 6 },
      rulesMessageJoinLabel: true,
      componentProps: ({}) => {
        return {
          api: list,
          params: {
            pageSize: 0,
          },
          resultField: 'data',
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'companyName', value: 'companyId' },
        };
      },
    },
    {
      field: 'tenantChildFlag',
      component: 'Select',
      label: '数据范围',
      colProps: { span: 4 },
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '本级', value: 0 },
          { label: '下一级', value: 1 },
          { label: '所有下级', value: 2 },
        ],
      }
    },
  ];
};

export const modalForm = (record?: Recordable): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'autoId',
      label: '主键id',
      required: false,
      show: false,
      component: 'InputNumber',
      className: '!w-full',
    },
    {
      field: 'companyId',
      label: '所属工会',
      required: true,
      itemProps: {
        autoLink: true,
      },
      component: 'ApiSelect',
      colProps: { span: 12 },
      rulesMessageJoinLabel: true,
      componentProps: ({}) => {
        return {
          // mode: 'multiple',
          api: list,
          params: {
            pageSize: 0,
          },
          resultField: 'data',
          immediate: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.companyName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'companyName', value: 'companyId' },
        };
      },
    },
    {
      field: 'parentId',
      label: '父级栏目',
      required: false,
      colProps: { span: 12 },
      component: 'ApiTreeSelect',
      rulesMessageJoinLabel: true,
      componentProps: function ({ formModel }) {
        return {
          api: getList,
          fieldNames: { label: 'categoryName', value: 'autoId', children: 'children' },
          params: {
            autoId: record?.autoId,
          },
          immediate: false,
          alwaysLoad: true,
          showSearch: true,
          treeNodeFilterProp: 'categoryName',
          getPopupContainer: () => document.body,
          onChange: e => {
            maxSortNumber({ parentId: formModel['parentId'], autoId: formModel['autoId'] }).then(
              res => {
                const { data } = res;
                formModel['sort'] = data;
              }
            );
          },
        };
      },
    },

    {
      field: 'categoryName',
      label: '栏目名称',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
      componentProps: function ({ formModel }) {
        return {
          autocomplete: 'off',
          showCount: true,
          maxlength: 20,
          onChange: e => {
            getCategoryNamePinyin({
              categoryName: formModel['categoryName'],
              autoId: formModel['autoId'],
            }).then(res => {
              const { data } = res;
              formModel['categoryCode'] = data;
            });
          },
        };
      },
    },
    // {
    //   field: 'displayName',
    //   label: '展示名称',
    //   component: 'Input',
    //   rulesMessageJoinLabel: true,
    //   dynamicDisabled: true,
    //   componentProps: {
    //     autocomplete: 'off',
    //     showCount: true,
    //     maxlength: 40,
    //   },
    // },
    {
      field: 'categoryCode',
      label: '栏目编码',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
      rules: [
        {
          required: true,
          validator: async (_, value) => {
            if (!value) {
              return Promise.reject('请输入栏目编码,且不能重复');
            }
            return Promise.resolve();
          },
          trigger: ['change', 'blur'],
        },
      ],
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入栏目编码',
        showCount: true,
        maxlength: 200,
      },
    },
    {
      field: 'sort',
      label: '序号',
      required: true,
      colProps: { span: 12 },
      component: 'InputNumber',
      className: '!w-full',
    },

    {
      field: 'platformType',
      label: '平台类型',
      required: true,
      colProps: { span: 12 },
      component: 'CheckboxGroup',
      defaultValue: ['GHAPP'],
      dynamicDisabled: true,
      componentProps: function ({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get('appType') as CheckboxOptionType[],
          onChange: e => {
            if (!e?.includes('GHAPP')) {
              formModel['appAbstractWhether'] = 'N';
              formModel['appCoverWhether'] = 'N';
              formModel['appMaxCoverNumber'] = undefined;
            } else if (!e?.includes('GHGW')) {
              formModel['gwAbstractWhether'] = 'N';
              formModel['gwCoverWhether'] = 'N';
              formModel['gwMaxCoverNumber'] = undefined;
            }
          },
        };
      },
    },
    {
      field: 'categorySortOrder',
      label: '栏目下新闻排序方式',
      required: true,
      dynamicDisabled: true,
      component: 'RadioGroup',
      colProps: { span: 12 },
      labelWidth: 170,
      defaultValue: 'publishAndTop',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('categorySortOrder') as RadioGroupChildOption[],
      },
    },
    {
      field: 'appAbstractWhether',
      label: '工会APP端资讯摘要必填',
      required: true,
      colProps: { span: 12 },
      component: 'RadioGroup',
      defaultValue: 'N',
      labelWidth: 200,
      ifShow: ({ values }) => {
        return values.platformType?.includes('GHAPP');
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('EnableOrDisable') as RadioGroupChildOption[],
      },
    },
    {
      field: 'appCoverWhether',
      label: '工会APP端封面图必传',
      required: true,
      component: 'RadioGroup',
      defaultValue: 'N',
      colProps: { span: 12 },
      labelWidth: 200,
      ifShow: ({ values }) => {
        return values.platformType?.includes('GHAPP');
      },
      componentProps: function ({ formModel }) {
        return {
          onChange: value => {
            if ('N' === value) {
              formModel['appMaxCoverNumber'] = undefined;
            }
          },
          options: dictionary.getDictionaryOpt.get('EnableOrDisable') as RadioGroupChildOption[],
        };
      },
    },
    {
      field: 'appMaxCoverNumber',
      label: '工会APP端封面图张数',
      colProps: { span: 12 },
      labelWidth: 200,
      className: '!w-full',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => {
        return values.platformType?.includes('GHAPP') && 'Y' === values.appCoverWhether;
      },
      componentProps: {
        min: 1,
        max: 3,
      },
    },
    {
      field: 'gwAbstractWhether',
      label: '工会官网端资讯摘要必填',
      required: true,
      component: 'RadioGroup',
      defaultValue: 'N',
      colProps: { span: 12 },
      labelWidth: 200,
      ifShow: ({ values }) => {
        return values.platformType?.includes('GHGW');
      },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('EnableOrDisable') as RadioGroupChildOption[],
      },
    },
    {
      field: 'gwCoverWhether',
      label: '工会官网端封面图必传',
      required: true,
      component: 'RadioGroup',
      defaultValue: 'N',
      colProps: { span: 12 },
      labelWidth: 200,
      ifShow: ({ values }) => {
        return values.platformType?.includes('GHGW');
      },
      componentProps: function ({ formModel }) {
        return {
          options: dictionary.getDictionaryOpt.get('EnableOrDisable') as RadioGroupChildOption[],
          onChange: value => {
            if ('N' === value) {
              formModel['gwMaxCoverNumber'] = undefined;
            }
          },
        };
      },
    },
    {
      field: 'gwMaxCoverNumber',
      label: '工会官网端封面图张数',
      colProps: { span: 12 },
      labelWidth: 200,
      className: '!w-full',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      ifShow: ({ values }) => {
        return values.platformType?.includes('GHGW') && 'Y' === values.gwCoverWhether;
      },
      componentProps: {
        min: 1,
        max: 3,
      },
    },
    {
      field: 'categoryType',
      label: '栏目类型',
      required: true,
      component: 'RadioGroup',
      colProps: { span: 12 },
      dynamicDisabled: true,
      defaultValue: 'UNIVERSAL',
      componentProps: {
        options: dictionary.getDictionaryOpt.get('categoryType') as RadioGroupChildOption[],
      },
      show: false,
    },
    {
      field: 'cuttingRatioStart',
      label: '工会APP封面图尺寸宽',
      required: true,
      colProps: { span: 12 },
      className: '!w-full',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      labelWidth: 200,
      defaultValue: '200',
      ifShow({ values }) {
        return values.categoryType === 'UNIVERSAL' && values.platformType?.includes('GHAPP');
      },
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'cuttingRatioEnd',
      label: '工会APP封面图尺寸高',
      required: true,
      colProps: { span: 12 },
      className: '!w-full',
      rulesMessageJoinLabel: true,
      defaultValue: '136',
      labelWidth: 200,
      component: 'InputNumber',
      ifShow({ values }) {
        return values.categoryType === 'UNIVERSAL' && values.platformType?.includes('GHAPP');
      },
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'gwCuttingRatioStart',
      label: '工会官网封面图尺寸宽',
      required: true,
      colProps: { span: 12 },
      className: '!w-full',
      component: 'InputNumber',
      labelWidth: 200,
      rulesMessageJoinLabel: true,
      defaultValue: '300',
      ifShow({ values }) {
        return values.categoryType === 'UNIVERSAL' && values.platformType?.includes('GHGW');
      },
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'gwCuttingRatioEnd',
      label: '工会官网封面图尺寸高',
      required: true,
      colProps: { span: 12 },
      className: '!w-full',
      rulesMessageJoinLabel: true,
      defaultValue: '180',
      labelWidth: 200,
      component: 'InputNumber',
      ifShow({ values }) {
        return values.categoryType === 'UNIVERSAL' && values.platformType?.includes('GHGW');
      },
      componentProps: {
        min: 1,
      },
    },
    {
      field: 'maxNewsNumber',
      label: '最多新增新闻数',
      colProps: { span: 12 },
      labelWidth: 130,
      dynamicDisabled: true,
      className: '!w-full',
      component: 'InputNumber',
      rulesMessageJoinLabel: true,
      componentProps: {
        min: 0,
      },
    },
    {
      field: 'categoryLogo',
      label: '栏目图片',
      colProps: { span: 12 },
      component: 'Upload',
      componentProps: {
        uploadParams: {
          operateType: 20,
        },
        maxSize: 50,
        maxNumber: 1,
        api: uploadApi,
        accept: ['image/*'],
      },
      rulesMessageJoinLabel: true,
    },
    {
      field: 'categoryDescription',
      label: '栏目简介',
      component: 'InputTextArea',
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps: {
        showCount: true,
        maxlength: 400,
      },
    },
  ];
};

//设置排序弹窗
export const modalSortFormItem = (autoId): FormSchema[] => {
  return [
    {
      field: 'categoryName',
      label: '当前栏目名称',
      component: 'ShowSpan',
      colProps: { span: 24 },
    },
    {
      field: 'referToAutoId',
      label: '参照栏目名称',
      component: 'ApiSelect',
      required: true,
      colProps: { span: 24 },
      itemProps: {
        autoLink: false,
      },
      rulesMessageJoinLabel: true,
      componentProps: () => {
        return {
          api: getReferToCategory,
          params: {
            autoId: autoId,
          },
          alwaysLoad: true,
          immediate: true,
          resultField: 'data',
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.categoryName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'categoryName', value: 'autoId' },
        };
      },
    },
    {
      field: 'sequentialOptions',
      label: '排序选项',
      component: 'RadioGroup',
      required: true,
      colProps: { span: 24 },
      rulesMessageJoinLabel: true,
      componentProps: {
        options: [
          { label: '之前', value: 'BEFORE' },
          { label: '之后', value: 'AFTER' },
        ],
      },
    },
  ];
};
